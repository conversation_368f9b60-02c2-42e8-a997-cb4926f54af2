<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('quotations', function (Blueprint $table) {
            $table->decimal('single_markup', 8, 2)->nullable()->after('child_markup');
            $table->decimal('double_markup', 8, 2)->nullable()->after('single_markup');
            $table->decimal('triple_markup', 8, 2)->nullable()->after('double_markup');
            $table->decimal('quadruple_markup', 8, 2)->nullable()->after('triple_markup');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('quotations', function (Blueprint $table) {
            $table->dropColumn(['single_markup', 'double_markup', 'triple_markup', 'quadruple_markup']);
        });
    }
};
