<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('attraction_attraction_type', function (Blueprint $table) {
            $table->id();
            $table->foreignId('attraction_id')->constrained('attraction')->onDelete('cascade');
            $table->foreignId('attraction_type_id')->constrained('attraction_types')->onDelete('cascade');
            $table->timestamps();

            // Ensure unique combination of attraction and type
            $table->unique(['attraction_id', 'attraction_type_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('attraction_attraction_type');
    }
};
