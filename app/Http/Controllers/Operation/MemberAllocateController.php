<?php

namespace App\Http\Controllers\Operation;

use App\Http\Controllers\Controller;
use App\Models\Member;
use App\Models\MemberAllocate;
use App\Models\Quotation;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\Config;

class MemberAllocateController extends Controller
{
  protected $colors;

  public function __construct()
  {
      $this->colors = Config::get('colors.color_codes'); // Fetch color codes from config
  }
  public function index()
  {
    $totalAllocations = MemberAllocate::count();
    return view('operation.member_allocates.index', compact('totalAllocations'));
  }

  public function create()
  {
    $allocations = new MemberAllocate();
    $quotations = Quotation::all();
    $members = Member::all();

    return view('operation.member_allocates.create', compact('allocations', 'quotations', 'members'));
  }

  public function store(Request $request)
  {
    $request->validate([
      'quotation_id' => 'required|exists:quotations,id',
      'driver_id' => 'required|exists:members,id',
    ]);

    MemberAllocate::create($request->all());
    return redirect()->back()->with('success', 'Member allocation created successfully!');
  }

  public function edit($id)
  {
    $memberAllocate = MemberAllocate::findOrFail($id);
    $quotations = Quotation::all();
    $members = Member::all();

    $quotation = Quotation::findOrFail($memberAllocate->quotation->id);

    // Get the first day
    $firstDay = $quotation->days->first();

    // Check if a day exists, and process its members
    if ($firstDay) {
      $allocatedMembers = $firstDay->members->map(function ($member) {
        return [
          'language' => $member->language->name ?? null,
          'position' => $member->position->name ?? null,
          'type' => $member->type->name ?? null,
        ];
      });
    } else {
      $allocatedMembers = [];
    }

    return view('operation.member_allocates.edit', compact('memberAllocate', 'quotations', 'members', 'allocatedMembers'));
  }

  public function update($id, Request $request)
  {
    $request->validate([
      'quotation_id' => 'required|exists:quotations,id',
      'driver_id' => 'required|exists:members,id',
    ]);

    $allocation = MemberAllocate::findOrFail($id);
    $allocation->update($request->all());

    return redirect()->back()->with('success', 'Member allocation updated successfully!');
  }

  public function getAllocations()
  {
    $authUser = auth()->user();

    $allocations = MemberAllocate::with(['driver', 'guide', 'quotation']);

    if ($authUser->hasAnyRole(['Super Admin', 'Admin', 'Travel Consultant', 'Accountant'])) {
      // Show all allocations for these roles
      $allocations = $allocations->get();
    } else {
        // Foreign Agent can only see their own quotation allocations or allocations where they are assigned as driver or guide
      $allocations = $allocations->where(function ($query) use ($authUser) {
          // Fetch quotation IDs for the logged-in user
          $quotationIds = Quotation::where('user_id', $authUser->id)->pluck('quotation_no');

          // Apply filtering conditions
          $query->whereIn('quotation_id', $quotationIds)
              ->orWhereHas('driver', function ($q) use ($authUser) {
                  $q->where('user_id', $authUser->id);
              })
              ->orWhereHas('guide', function ($q) use ($authUser) {
                  $q->where('user_id', $authUser->id);
              });
      })->get();
    }

    // Map the allocations to the FullCalendar event format
    $events = $allocations->flatMap(function ($allocation) {
      $events = [];
      // Event for the Driver
      if ($allocation->driver) {
        $events[] = [
          'id' => $allocation->id, // Unique ID for the allocation
          'title' => 'Driver: ' . $allocation->driver->name??'' . ' #' . $allocation->quotation->quotation_no,
          'start' => $allocation->start_date,
          'end' => $allocation->end_date && $allocation->end_date !== 'N/A'
                    ? Carbon::parse($allocation->end_date)->addDay()->toDateString()
                    : $allocation->start_date,
          'color' => $this->colors[$allocation->driver->id] ?? '#28a745', // Blue for drivers
          'extendedProps' => [
            'quotation' => $allocation->quotation->quotation_no ?? null,
          ],
        ];
      }

      // Event for the Guide
      if ($allocation->guide) {
        $events[] = [
          'id' => $allocation->id, // Unique ID for the allocation
          'title' => 'Guide: ' . $allocation->guide->name??'' . ' #' . $allocation->quotation->quotation_no,
          'start' => $allocation->start_date,
          'end' => $allocation->end_date && $allocation->end_date !== 'N/A'
                    ? Carbon::parse($allocation->end_date)->addDay()->toDateString()
                    : $allocation->start_date,
          'color' => $this->colors[$allocation->guide->id] ?? '#28a745', // Green for guides
          'extendedProps' => [
            'quotation' => $allocation->quotation->quotation_no ?? null,
          ],
        ];
      }

      return $events;
    });

    return response()->json($events);
  }

  public function destroy($id)
  {
    $memberAllocate = MemberAllocate::findOrFail($id);
    $memberAllocate->delete();

    return response()->json(['success' => true, 'message' => 'Member Allocation deleted successfully.']);
  }
}
