<?php

namespace App\Http\Controllers\Quotation;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\QuotationComment;
use App\Models\Quotation;
use App\Models\User;
use App\Models\RunningJob;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class QuotationCommentController extends Controller
{
  public function store(Request $request, $quotationId)
  {
    $request->validate([
      'comment' => 'required|string',
      'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
      'attachments.*' => 'mimes:pdf|max:5120',
    ]);

    $images = [];
    if ($request->hasFile('images')) {
      foreach ($request->file('images') as $image) {
        $images[] = $image->store('quotation_comments/images', 'public');
      }
    }

    $attachments = [];
    if ($request->hasFile('attachments')) {
      foreach ($request->file('attachments') as $attachment) {
        $attachments[] = $attachment->store('quotation_comments/attachments', 'public');
      }
    }

    $comment = QuotationComment::create([
      'quotation_id' => $quotationId,
      'user_id' => auth()->id(),
      'name' => auth()->check() ? auth()->user()->name : 'Customer',
      'comment' => $request->comment,
      'images' => json_encode($images),
      'attachments' => json_encode($attachments),
    ]);

    // Send notifications (email and WhatsApp)
    $this->sendNotifications($comment, $quotationId);

    return response()->json([
      'success' => true,
      'message' => 'Comment added successfully.',
      'comment' => [
        'id' => $comment->id,
        'user_name' => $comment->name,
        'comment' => nl2br(e($comment->comment)),
        'created_at' => $comment->created_at->format('h:i A, M d Y'),
        'images' => array_map(function($img) { return asset('storage/' . $img); }, $images),
        'attachments' => array_map(function($att) { return asset('storage/' . $att); }, $attachments),
      ]
    ]);
  }

  /**
   * Send notifications for new comment
   */
  private function sendNotifications(QuotationComment $comment, $quotationId)
  {
    try {
      // Get the quotation
      $quotation = Quotation::find($quotationId);
      if (!$quotation) {
        return;
      }

      // Get all recipients for the notification
      $recipients = collect();

      // Get running job and its assignees
      $runningJob = RunningJob::where('quotation_id', $quotationId)->first();
      if ($runningJob) {
        // Add quotation assignees
        if ($runningJob->quotationAssignees && $runningJob->quotationAssignees->count() > 0) {
          $recipients = $recipients->merge($runningJob->quotationAssignees);
        }

        // Add booking assignee if exists
        if ($runningJob->booking && $runningJob->booking->assignee) {
          $recipients->push($runningJob->booking->assignee);
        }
      }

      // Add customer email if exists (client_name stores customer user ID)
      if ($quotation->client_name) {
        $customer = User::find($quotation->client_name);
        if ($customer && $customer->email) {
          $recipients->push($customer);
        }
      }

      // Remove duplicates and the current user
      $recipients = $recipients->unique('id')->filter(function ($user) {
        return $user->id !== Auth::user()->id;
      });

      // Send notifications if we have recipients
      if ($recipients->count() > 0) {
        $senderName = Auth::user()->name;
        $quotationArray = $quotation->toArray();

        // Load user relationship for the comment
        $comment->load('user');

        // Send email notifications
        foreach ($recipients as $recipient) {
          if ($recipient->email) {
            Mail::to($recipient->email)->queue(
              new \App\Mail\ItineraryCommentNotification($comment, $quotationArray, $senderName)
            );
          }
        }

        // Send WhatsApp notifications
        \App\Jobs\SendWhatsAppNotification::dispatch($recipients, $comment, $quotationArray, $senderName);
      }
    } catch (\Exception $e) {
      // Log error but don't fail the comment creation
      Log::error('Failed to send comment notifications', [
        'comment_id' => $comment->id,
        'quotation_id' => $quotationId,
        'error' => $e->getMessage()
      ]);
    }
  }
}
