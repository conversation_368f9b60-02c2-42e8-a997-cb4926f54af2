<?php
namespace App\Http\Controllers\Quotation;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Quotation;
use App\Models\QuotationDayHotel;
use App\Models\QuotationDayAttraction;
use App\Models\QuotationDayHotelDocument;
use App\Models\QuotationDayAttractionDocument;
use App\Models\RunningJob;
use App\Models\Booking;

class QuotationDocumentsController extends Controller
{
    public function index()
    {
        return view('quotation.doc.show_documents');
    }

    public function show()
    {
        $quotationNo = $_GET['id'] ?? null;
        $quotation = Quotation::where('reference_no', $quotationNo)->firstOrFail();

        $hotels = QuotationDayHotel::whereHas('quotationDay', function ($query) use ($quotation) {
            $query->where('quotation_id', $quotation->id);
        })
        ->with(['hotel', 'documents'])
        ->selectRaw('MIN(id) as id, hotel_id')
        ->groupBy('hotel_id')
        ->with(['hotel', 'documents'])
        ->get();

        $attractions = QuotationDayAttraction::whereHas('quotationDay', function ($query) use ($quotation) {
            $query->where('quotation_id', $quotation->id);
        })->with(['attraction', 'documents'])->get();

        // Get booking information through RunningJob
        $booking = null;
        $runningJob = RunningJob::where('quotation_id', $quotation->quotation_no)
            ->with('booking')
            ->first();

        if ($runningJob && $runningJob->booking) {
            $booking = $runningJob->booking;
        }

        // Assign Image URL to each hotel
        foreach ($hotels as $hotel) {
            if ($hotel->documents) {
              foreach ($hotel->documents as $document) {
                $document->file_path = (new \App\Models\Hotel())->getImageUrl($document->file_path ?? '');
              }
            }
        }

        // Assign Image URL to each attraction (if needed)
        foreach ($attractions as $attraction) {
            if ($attraction->documents) {
              foreach ($attraction->documents as $document) {
                $document->file_path = (new \App\Models\Attraction())->getImageUrl($document->file_path ?? '');
              }
            }
        }

        return response()->json([
            'hotels' => $hotels,
            'attractions' => $attractions,
            'booking' => $booking
        ]);
    }

    public function storeHotelDocument(Request $request, $hotelId)
    {
        $request->validate([
            'files' => 'required|array',
            'files.*' => 'file|mimes:jpeg,png,jpg,gif,pdf|max:2048',
        ]);

        $uploadedFiles = [];

        foreach ($request->file('files') as $file) {
            $filePath = $file->store('quotation_documents/hotels', 'public');

            $document = QuotationDayHotelDocument::create([
                'quotation_day_hotel_id' => $hotelId,
                'file_path' => $filePath,
                'type' => $file->getClientOriginalExtension(),
            ]);

            $uploadedFiles[] = [
                'id' => $document->id,
                'file_path' => asset('storage/' . $filePath),
                'type' => $file->getClientOriginalExtension(),
            ];
        }

        return response()->json([
            'status' => true,
            'message' => "Documents uploaded successfully!",
            'files' => $uploadedFiles
        ]);
    }


    public function storeAttractionDocument(Request $request, $attractionId)
    {
        $request->validate([
            'files' => 'required|array',
            'files.*' => 'file|mimes:jpeg,png,jpg,gif,pdf|max:2048',
        ]);

        $uploadedFiles = [];

        foreach ($request->file('files') as $file) {
            $filePath = $file->store('quotation_documents/attractions', 'public');

            $document = QuotationDayAttractionDocument::create([
                'quotation_day_attraction_id' => $attractionId,
                'file_path' => $filePath,
                'type' => $file->getClientOriginalExtension(),
            ]);

            $uploadedFiles[] = [
                'id' => $document->id,
                'file_path' => asset('storage/' . $filePath),
                'type' => $file->getClientOriginalExtension(),
            ];
        }

        return response()->json([
            'status' => true,
            'message' => "Documents uploaded successfully!",
            'files' => $uploadedFiles
        ]);
    }

}
