<?php

namespace App\Http\Controllers\Report;

use App\Http\Controllers\Controller;
use App\Models\Quotation;
use App\Models\User;
use App\Models\QuotationDayHotel;
use App\Services\HotelService;
use App\Models\MemberAllocate;
use App\Models\QuotationDayTransport;
use App\Models\Member;
use App\Models\Hotel;
use App\Models\Attraction;
use App\Models\QuotationDay;
use App\Models\QuotationDayAttraction;
use App\Models\HotelRoomCategory;
use Illuminate\Http\Request;

class ReportController extends Controller
{
  public function quotation(Request $request)
  {
      // Initialize query with relationships
      $query = Quotation::withCount('days')
          ->whereHas('days.hotels', function ($query) use ($request) {
              if ($request->has('room_category') && !empty($request->input('room_category'))) {
                  $query->where('room_category', $request->input('room_category'));
              }
          });

      // Apply filters for `created_at` date range
      if ($request->input('created_from') && $request->input('created_to')) {
          $query->whereBetween('created_at', [
              $request->input('created_from'),
              $request->input('created_to')
          ]);
      }

      // Apply filters for `arrival_date` date range
      if ($request->input('arrival_from') && $request->input('arrival_to')) {
          $query->whereBetween('arrival_date', [
              $request->input('arrival_from'),
              $request->input('arrival_to')
          ]);
      }

      // Filter by file handler (user_id)
      if ($request->has('user_id') && !empty($request->input('user_id'))) {
          $query->where('user_id', $request->input('user_id'));
      }

      // Filter by status (save_type)
      if ($request->has('save_type') && !empty($request->input('save_type'))) {
          $query->where('save_type', $request->input('save_type'));
      }

      // Filter by number of days
      if ($request->has('days_count') && !empty($request->input('days_count'))) {
          $query->having('days_count', '=', $request->input('days_count'));
      }

      $quotations = $query->orderBy('id', 'desc')->paginate(10)->withPath('')->withQueryString();

      // Fetch all users for the dropdown
      $users = User::all();

      // Fetch all room categories for the dropdown
      $roomCategories = HotelRoomCategory::all();

      return view('reports.quotation', compact('quotations', 'users', 'roomCategories'));
  }



  public function hotelBookings(Request $request)
  {
      // Initialize query
      $query = QuotationDayHotel::with(['quotationDay.quotation', 'hotel', 'quotationDay']);
      $hotels = Hotel::pluck('name', 'id');

      // Apply filters for `date` range
      if ($request->input('date_from') && $request->input('date_to')) {
          $query->whereHas('quotationDay', function ($q) use ($request) {
              $q->whereBetween('date', [
                  $request->input('date_from'),
                  $request->input('date_to')
              ]);
          });
      }

      // Filter by hotel name
      if ($request->input('hotel_id')) {
          $query->whereHas('hotel', function ($q) use ($request) {
              $q->where('id', '=', $request->input('hotel_id'));
          });
      }

      // Filter by quotation no
      if ($request->input('reference_no')) {
          $query->whereHas('quotationDay.quotation', function ($q) use ($request) {
              $q->where('reference_no', '=', $request->input('reference_no'));
          });
      }

      // Conditional logic based on hotel.own_arrangement
      $query->whereHas('hotel', function ($q) {
        $q->where('own_arrangement', 1);
      })->whereHas('quotationDay', function ($q) {
        $q->whereNotIn('id', function ($subquery) {
            $subquery->selectRaw('MAX(id)')
                ->from('quotation_days')
                ->groupBy('quotation_id');
        });
      });

      $query->orWhereHas('hotel', function ($q) {
        $q->where('own_arrangement', 0);
      });

      // Group by `quotation_id` and `hotel_id` and calculate totals for each
      $hotelBookings = $query->get()->groupBy(function ($item) {
          // Add null checks to prevent "Attempt to read property "id" on null" error
          if (!$item->quotationDay || !$item->quotationDay->quotation) {
              return 'unknown-' . ($item->hotel_id ?? 'unknown');
          }
          return $item->quotationDay->quotation->id . '-' . $item->hotel_id;
      })->map(function ($bookings) {
          $totalRoomRate = $totalChildRate = $nights = 0;
          $roomArrangements = [];
          $childRates = [];
          $hotelData = $bookings->first();

          foreach ($bookings as $booking) {
              $nights = $booking->nights ?? 1;


              $totalChildRate += ($booking->cwb_rate * ($booking->quotationDay->quotation->cwb ?? 0)) +
                  ($booking->cnb_rate * ($booking->quotationDay->quotation->cnb ?? 0));

              if ($booking->single > 0) {
                  if($booking->discounted_adult_rate_single) {
                    $roomArrangements[] = "Single: {$booking->single} x {$booking->discounted_adult_rate_single}";
                    $totalRoomRate += ($booking->discounted_adult_rate_single * $booking->single);
                  } else {
                    $roomArrangements[] = "Single: {$booking->single} x {$booking->adult_rate_single}";
                    $totalRoomRate += ($booking->adult_rate_single * $booking->single);
                  }

              }
              if ($booking->double > 0) {
                  if($booking->discounted_adult_rate_double) {
                    $roomArrangements[] = "Single: {$booking->double} x {$booking->discounted_adult_rate_double}";
                    $totalRoomRate += ($booking->discounted_adult_rate_double * $booking->double);
                  } else {
                    $roomArrangements[] = "Double: {$booking->double} x {$booking->adult_rate_double}";
                    $totalRoomRate += ($booking->adult_rate_double * $booking->double);
                  }
              }
              if ($booking->triple > 0) {
                  if($booking->discounted_adult_rate_triple) {
                    $roomArrangements[] = "Single: {$booking->triple} x {$booking->discounted_adult_rate_triple}";
                    $totalRoomRate += ($booking->discounted_adult_rate_triple * $booking->triple);
                  } else {
                    $roomArrangements[] = "Triple: {$booking->triple} x {$booking->adult_rate_triple}";
                    $totalRoomRate += ($booking->adult_rate_triple * $booking->triple);
                  }
              }
              if ($booking->quadruple > 0) {
                  if($booking->discounted_adult_rate_quadruple) {
                    $roomArrangements[] = "Single: {$booking->quadruple} x {$booking->discounted_adult_rate_quadruple}";
                    $totalRoomRate += ($booking->discounted_adult_rate_quadruple * $booking->quadruple);
                  } else {
                    $roomArrangements[] = "Quadruple: {$booking->quadruple} x {$booking->adult_rate_quadruple}";
                    $totalRoomRate += ($booking->adult_rate_quadruple * $booking->quadruple);
                  }
              }

              if ($booking->cwb_rate > 0) {
                  $cwb = $booking->quotationDay->quotation->cwb ?? 0;
                  $childRates[] = "CWB: {$cwb} x {$booking->cwb_rate}";
              }
              if ($booking->cnb_rate > 0) {
                  $cnb = $booking->quotationDay->quotation->cnb ?? 0;
                  $childRates[] = "CNB: {$cnb} x {$booking->cnb_rate}";
              }
          }

          return [
              'reference_no' => $hotelData->quotationDay->quotation->reference_no ?? 'N/A',
              'hotel_name' => $hotelData->hotel->name ?? 'N/A',
              'checkin_date' => $hotelData->quotationDay->date ?? 'N/A',
              'checkout_date' => HotelService::getCheckoutDate($hotelData->quotationDay->date ?? 'N/A', $nights),
              'nights' => $nights,
              'room_category' => \App\Models\HotelRoomCategory::find($hotelData->room_category)->name ?? 'N/A',
              'meal_type' => \App\Models\HotelMeal::find($hotelData->meal)->type ?? 'N/A',
              'room_arrangement' => $roomArrangements,
              'child_rates' => $childRates,
              'total_rate' => $totalRoomRate + $totalChildRate,
          ];
      });

      // Calculate grand total
      $grandTotal = $hotelBookings->sum('total_rate');

      return view('reports.hotelBookings', compact('hotelBookings', 'grandTotal', 'hotels'));
  }

  public function transportReport(Request $request)
  {
      // Initialize query
      $query = MemberAllocate::with(['quotation', 'driver', 'guide']);
      $members = Member::all();

      // Filter by Quotation reference_no
      if ($request->input('reference_no')) {
        $query->whereHas('quotation', function ($q) use ($request) {
            $q->where('reference_no', $request->input('reference_no'));
        });
      }

      // Filter by Date Range
      if ($request->input('start_date') && $request->input('end_date')) {
          $query->whereBetween('start_date', [
              $request->input('start_date'),
              $request->input('end_date')
          ]);
      }

      // Filter by Member (Driver or Guide)
      if ($request->input('member_id')) {
          $query->where(function ($q) use ($request) {
              $q->where('driver_id', $request->input('member_id'))
                  ->orWhere('guide_id', $request->input('member_id'));
          });
      }

      // Fetch the data and group by quotation_id
      $allocations = $query->get()->groupBy('quotation_id')->map(function ($allocations) {
          $totalGuideRate = $totalDriverRate = $totalVehicleRate = $totalDays = 0;
          $quotationData = [];
          $quotation = $allocations->first()->quotation;

          foreach ($allocations as $allocation) {
              $quotationDays = QuotationDay::where('quotation_id', $allocation->quotation_id)->get();

              foreach ($quotationDays as $day) {
                  $distance = $day->transports->distance ?? 0; // Distance
                  $rate = $day->transports->rate ?? 0;        // Rate per km

                  // Member rates
                  $driverRate = $day->members->where('position_id', 1)->sum('member_rate'); // Driver rate
                  $guideRate = $day->members->where('position_id', 2)->sum('member_rate');  // Guide rate

                  // Aggregate data
                  $totalGuideRate += $guideRate;
                  $totalDriverRate += $driverRate;
                  $totalVehicleRate += ($distance * $rate);
                  $totalDays++;
              }
          }

          return [
              'reference_no' => $quotation->reference_no ?? 'N/A',
              'driver_name' => $allocations->first()->driver->name ?? 'N/A',
              'guide_name' => $allocations->first()->guide->name ?? 'N/A',
              'total_guide_rate' => $totalGuideRate,
              'total_driver_rate' => $totalDriverRate,
              'total_vehicle_rate' => $totalVehicleRate,
              'total_days' => $totalDays,
              'grand_total' => $totalGuideRate + $totalDriverRate + $totalVehicleRate,
          ];
      });

      // Calculate overall totals
      $totalGuideRate = $allocations->sum('total_guide_rate');
      $totalDriverRate = $allocations->sum('total_driver_rate');
      $totalVehicleRate = $allocations->sum('total_vehicle_rate');
      $grandTotal = $allocations->sum('grand_total');

      return view('reports.transportReport', compact(
          'allocations',
          'members',
          'totalGuideRate',
          'totalDriverRate',
          'totalVehicleRate',
          'grandTotal'
      ));
  }

  public function activityReport(Request $request)
  {
      // Initialize query
      $query = QuotationDayAttraction::with(['quotationDay.quotation', 'attraction', 'quotationDay']);
      $attractions = Attraction::pluck('name', 'id');

      // Apply filters for `date` range
      if ($request->input('date_from') && $request->input('date_to')) {
          $query->whereHas('quotationDay', function ($q) use ($request) {
              $q->whereBetween('date', [
                  $request->input('date_from'),
                  $request->input('date_to')
              ]);
          });
      }

      // Filter by attraction name
      if ($request->input('activity_id')) {
          $query->whereHas('attraction', function ($q) use ($request) {
              $q->where('id', '=', $request->input('activity_id'));
          });
      }

      // Filter by quotation no
      if ($request->input('quotation_no')) {
          $query->whereHas('quotationDay.quotation', function ($q) use ($request) {
              $q->where('id', '=', $request->input('quotation_no'));
          });
      }

      // Exclude the last day of each quotation
      $query->whereHas('quotationDay', function ($q) {
          $q->whereNotIn('id', function ($subquery) {
              $subquery->selectRaw('MAX(id)')
                  ->from('quotation_days')
                  ->groupBy('quotation_id');
          });
      });

      // Group by `quotation_id` and `attraction_id` and calculate totals for each
      $activityReports = $query->get()->groupBy(function ($item) {
          // Add null checks to prevent "Attempt to read property "id" on null" error
          if (!$item->quotationDay || !$item->quotationDay->quotation) {
              return 'unknown-' . ($item->attraction_id ?? 'unknown');
          }
          return $item->quotationDay->quotation->id . '-' . $item->attraction_id;
      })->map(function ($activities) {
          $totalAdultRate = $totalChildRate = 0;
          $attractionData = $activities->first();

          foreach ($activities as $activity) {
              $totalAdultRate += $activity->adult_rate * ($activity->quotationDay->quotation->adults ?? 0);
              $totalChildRate += $activity->child_rate * ($activity->quotationDay->quotation->children ?? 0);
          }

          return [
              'reference_no' => $attractionData->quotationDay->quotation->reference_no ?? 'N/A',
              'attraction_name' => $attractionData->attraction->name ?? 'N/A',
              'date' => $attractionData->quotationDay->date ?? 'N/A',
              'distance' => $attractionData->distance ?? 'N/A',
              'adult_rate' => $totalAdultRate,
              'child_rate' => $totalChildRate,
              'total_rate' => $totalAdultRate + $totalChildRate,
          ];
      });

      // Calculate grand total
      $grandTotal = $activityReports->sum('total_rate');

      return view('reports.activityReport', compact('activityReports', 'grandTotal', 'attractions'));
  }

}
