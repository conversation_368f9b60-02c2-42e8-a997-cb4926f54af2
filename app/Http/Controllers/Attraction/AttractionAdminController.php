<?php

namespace App\Http\Controllers\Attraction;

use App\Http\Controllers\Controller;
use App\Models\Attraction;
use App\Models\Place;
use App\Models\AttractionType;
use Illuminate\Http\Request;

class AttractionAdminController extends Controller
{
  public function index()
  {
    // Get the total number of attractions
    $totalAttractions = Attraction::count();
    $places = Place::all();

    return view('admin.attractions.index', compact('totalAttractions', 'places'));
  }

  public function show($id)
  {
    // Find the attraction by ID
    $attraction = Attraction::findOrFail($id);

    // Return the view with the attraction data
    return view('admin.attractions.show', compact('attraction'));
  }

  public function create()
  {
    // Fetch all types and places for the form
    $attractionTypes = AttractionType::all();
    $places = Place::where('type', 1)->orderBy('name', 'asc')->get();

    return view('admin.attractions.create', compact('attractionTypes', 'places'));
  }

  public function store(Request $request)
  {
    try {
      // Validate the input
      $request->validate([
        'name' => 'required|string|max:255',
        'es_name' => 'nullable|string|max:255',
        'fr_name' => 'nullable|string|max:255',
        'it_name' => 'nullable|string|max:255',
        'place' => 'required|exists:places,id',
        'attraction_types' => 'required|array|min:1',
        'attraction_types.*' => 'exists:attraction_types,id',
        'description' => 'nullable|string',
        'es_description' => 'nullable|string',
        'fr_description' => 'nullable|string',
        'it_description' => 'nullable|string',
        'duration' => 'nullable|integer',
        'address' => 'nullable|string|max:255',
        'point' => 'nullable|integer',
        'prefered' => 'boolean',
        'opening' => ['nullable', 'regex:/^\d{2}:\d{2}(:\d{2})?$/'],
        'closing' => ['nullable', 'regex:/^\d{2}:\d{2}(:\d{2})?$/'],
        'distance' => 'nullable|numeric',
        'time' => 'nullable|string|max:255',
        'latitude' => 'nullable|numeric',
        'longitude' => 'nullable|numeric',
        'email' => 'nullable|email', // New validation rule
        'phone' => 'nullable|string|max:20', // New validation rule
        'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
      ]);

      $data = $request->except(['attraction_types']);

      // Handle image upload
      if (isset($data['server_image'])) {
        $data['image'] = $data['server_image'];
      } else {
        if ($request->hasFile('image')) {
          $data['image'] = (new Place)->uploadImage($request->file('image'), 'attractions');
        }
      }

      $attraction = Attraction::create($data);

      // Attach the selected attraction types
      $attraction->attractionTypes()->attach($request->attraction_types);

      return redirect()->back()->with('success', 'Attraction created successfully!');
    } catch (\Illuminate\Validation\ValidationException $e) {
      return redirect()->back()
        ->withErrors($e->validator)
        ->withInput()
        ->with('error', 'Failed to create attraction! Please correct the errors.');
    }
  }

  public function edit($id)
  {
    // Find the attraction by ID with its types
    $attraction = Attraction::with('attractionTypes')->findOrFail($id);
    $attractionTypes = AttractionType::all();
    $places = Place::where('type', 1)->orderBy('name', 'asc')->get();

    return view('admin.attractions.edit', compact('attraction', 'attractionTypes', 'places'));
  }

  public function update($id, Request $request)
  {
    try {
      // Validate the input
      $request->validate([
        'name' => 'required|string|max:255',
        'es_name' => 'nullable|string|max:255',
        'fr_name' => 'nullable|string|max:255',
        'it_name' => 'nullable|string|max:255',
        'place' => 'required|exists:places,id',
        'attraction_types' => 'required|array|min:1',
        'attraction_types.*' => 'exists:attraction_types,id',
        'description' => 'nullable|string',
        'es_description' => 'nullable|string',
        'fr_description' => 'nullable|string',
        'it_description' => 'nullable|string',
        'duration' => 'nullable|integer',
        'address' => 'nullable|string|max:255',
        'point' => 'nullable|integer',
        'prefered' => 'boolean',
        'opening' => ['nullable', 'regex:/^\d{2}:\d{2}(:\d{2})?$/'],
        'closing' => ['nullable', 'regex:/^\d{2}:\d{2}(:\d{2})?$/'],
        'distance' => 'nullable|numeric',
        'time' => 'nullable|string|max:255',
        'latitude' => 'nullable|numeric',
        'longitude' => 'nullable|numeric',
        'email' => 'nullable|email', // New validation rule
        'phone' => 'nullable|string|max:20', // New validation rule
        'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
      ]);

      // Find the attraction by ID
      $attraction = Attraction::findOrFail($id);

      $data = $request->except(['attraction_types']);
      // Handle image upload
      if (isset($data['server_image'])) {
        $data['image'] = $data['server_image'];
      } else {
        if ($request->hasFile('image')) {
          // Delete old image
          $attraction->deleteImage($attraction->image);

          $data['image'] = $attraction->uploadImage($request->file('image'), 'attractions');
        }
      }

      // Update the attraction details
      $attraction->update($data);

      // Sync the selected attraction types (this will remove old ones and add new ones)
      $attraction->attractionTypes()->sync($request->attraction_types);

      return redirect()->back()->with('success', 'Attraction updated successfully!');
    } catch (\Illuminate\Validation\ValidationException $e) {
      return redirect()->back()
        ->withErrors($e->validator)
        ->withInput()
        ->with('error', 'Failed to update attraction! Please correct the errors.');
    }
  }

  public function deleteImage($id)
  {
      try {
          $attraction = Attraction::findOrFail($id);

          if ($attraction->image) {
              // Delete the image file
              $attraction->deleteImage($attraction->image);

              // Remove the image path from the database
              $attraction->update(['image' => null]);

              return response()->json(['success' => true, 'message' => 'Image deleted successfully']);
          }

          return response()->json(['success' => false, 'message' => 'No image found']);
      } catch (\Exception $e) {
          return response()->json(['success' => false, 'message' => 'Failed to delete image']);
      }
  }


  public function getAttractions(Request $request)
  {
    if ($request->ajax()) {
      $searchValue = $request->input('search.value');
      $orderColumnIndex = $request->input('order.0.column');
      $orderDirection = $request->input('order.0.dir');
      $columns = $request->input('columns');
      $orderColumn = $columns[$orderColumnIndex]['data'];

      // Query attractions with search and pagination
      $attractions = Attraction::with('placeR', 'typeR')
        ->when($searchValue, function ($query) use ($searchValue) {
          return $query->where('name', 'like', "%{$searchValue}%");
        })
        ->when($request->place, function ($query) use ($request) {
            $query->where('place', $request->place);
        })
        ->orderBy($orderColumn, $orderDirection)
        ->paginate($request->input('length'), ['*'], 'page', $request->input('start') / $request->input('length') + 1);

      // Prepare the data for DataTables
      $attractionsData = $attractions->map(function ($attraction) {
        return [
          'id' => $attraction->id,
          'name' => $attraction->name,
          'place' => $attraction->placeR->name ?? 'N/A',
          'type' => $attraction->typeR->type ?? 'N/A',
          'latitude' => $attraction->latitude ?? 'N/A',
          'longitude' => $attraction->longitude ?? 'N/A',
          'prefered' => $attraction->prefered ? 'Yes' : 'No',
        ];
      });

      // Return the JSON response in DataTables format
      return response()->json([
        'draw' => intval($request->input('draw')),
        'recordsTotal' => $attractions->total(),
        'recordsFiltered' => $attractions->total(),
        'data' => $attractionsData,
      ]);
    }
  }

  public function destroy($id)
  {
    // Find the attraction by ID
    $attraction = Attraction::findOrFail($id);

    // Delete the attraction
    $attraction->delete();

    return response()->json(['success' => true]);
  }
}
