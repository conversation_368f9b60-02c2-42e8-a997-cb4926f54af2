<?php

namespace App\Http\Controllers\Transport;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Market;
use App\Services\QuotationService;
use App\Services\HotelService;
use App\Models\Place;


class TransportController extends Controller
{
  protected $quotationService;
  public function __construct(QuotationService $quotationService)
  {
    $this->quotationService = $quotationService;
  }

  public function getVehicleRate(Request $request)
  {
    $pax = $request->input('pax');
    $countryID = $request->input('countryID');
    $arrivalDate = $request->input('arrivalDate');

    // Convert the arrival date to the correct format if necessary
    if (empty($arrivalDate) || $arrivalDate === 'N/A' || $arrivalDate === null) {
      return response()->json(['error' => 'Invalid arrival date provided'], 400);
    }

    try {
      $arrivalDate = \Carbon\Carbon::parse($arrivalDate)->format('Y-m-d');
    } catch (\Exception $e) {
      return response()->json(['error' => 'Invalid date format provided'], 400);
    }

    // Query to get the matching vehicle rate
    $vehicleRate = \App\Models\VehicleRate::with('vehicleType')
      ->where('pax_min', '<=', $pax)
      ->where('pax_max', '>=', $pax)
      ->where('country', $countryID)
      ->where('start_date', '<=', $arrivalDate)
      ->where('end_date', '>=', $arrivalDate)
      ->first(); // Get only the first matching result

    if ($vehicleRate) {
      // Return or process the found vehicle rate
      return response()->json(['success' => true, 'vehicleRate' => $vehicleRate]);
    } else {
      // Handle no matching result
      return response()->json(['success' => false, 'message' => 'No matching vehicles found.']);
    }
  }

  public function calculateDistance(Request $request)
  {
    $placeIds = $request->input('places');
    $places = Place::findMany($placeIds)->keyBy('id');

    // Reorder according to $placeIds
    $orderedPlaces = collect($placeIds)->map(function ($id) use ($places) {
        return $places->get($id);
    });

    $result = $this->quotationService->getDrivingDistanceWithWaypoints($orderedPlaces);
    $distance = $result['distance'];
    if (session()->has('data.quotation')) {
      $quotation = session('data.quotation');
      $dates = $this->quotationService->getDatesBetween($quotation);
      if(count($dates) > 12) {
          $distance = $distance + ($distance * 20 / 100);
      }
    } else {
      session()->flash('session_expired', true);
      return redirect()->route('quotation.start');
    }

    return response()->json(['distance' => $distance]);
  }
}
