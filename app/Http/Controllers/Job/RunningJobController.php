<?php

namespace App\Http\Controllers\Job;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\RunningJob;
use App\Models\JobEmail;
use App\Models\Quotation;
use App\Models\User;
use Illuminate\Support\Facades\Mail;
use App\Mail\ClientEmail;

class RunningJobController extends Controller
{
  public function index()
  {
    $runningJobs = RunningJob::with(['handler', 'quotation'])->get();
    return view('running_jobs.index', compact('runningJobs'));
  }

  public function create()
  {
    $users = User::all();
    return view('running_jobs.create', compact('users'));
  }

  public function store(Request $request)
  {
    $request->validate([
      'description' => 'required|string|max:255',
      'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
      'attachments.*' => 'file|mimes:pdf|max:10240',
    ]);

    $runningJob = new RunningJob();
    $runningJob->quotation_id = $request->quotation_id;
    $runningJob->handler_id = auth()->id();

    if ($request->hasFile('images')) {
      $images = [];
      foreach ($request->file('images') as $image) {
        $path = $image->store('running_jobs/images', 'public');
        $images[] = $path;
      }
      $runningJob->images = json_encode($images);
    }

    if ($request->hasFile('attachments')) {
      $attachments = [];
      foreach ($request->file('attachments') as $attachment) {
        $path = $attachment->store('running_jobs/attachments', 'public');
        $attachments[] = $path;
      }
      $runningJob->attachments = json_encode($attachments);
    }

    $runningJob->save();

    return redirect()->route('running_jobs.runningJobs.index')->with('success', 'Running Job created successfully.');
  }


  public function show(RunningJob $runningJob)
  {
    $emails = $runningJob->emails()->latest()->get();
    return view('running_jobs.show', compact('runningJob', 'emails'));
  }

  public function sendEmail(Request $request, RunningJob $runningJob)
  {
    $request->validate([
      'from' => 'required|string|max:255',
      'subject' => 'required|string|max:255',
      'body' => 'required|string',
    ]);

    JobEmail::create([
      'from' => $request->from,
      'subject' => $request->subject,
      'body' => $request->body,
      'running_job_id' => $request->running_job_id,
      'sender_id' => auth()->id(),
    ]);

    // Mail::to($request->recipient_email)->send(new ClientEmail($request->recipient_email, $request->subject, $request->body));

    return redirect()->back()->with('success', 'Email sent successfully.');
  }

  public function addConversation(Request $request, $runningJobId)
  {
    $request->validate([
      'description' => 'required|string',
      'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
      'attachments.*' => 'mimes:pdf|max:5120',
    ]);

    $runningJob = RunningJob::with(['booking', 'quotation'])->findOrFail($runningJobId);

    $images = [];
    if ($request->hasFile('images')) {
      foreach ($request->file('images') as $image) {
        $images[] = $image->store('running_jobs/images', 'public');
      }
    }

    $attachments = [];
    if ($request->hasFile('attachments')) {
      foreach ($request->file('attachments') as $attachment) {
        $attachments[] = $attachment->store('running_jobs/attachments', 'public');
      }
    }

    $conversation = $runningJob->conversations()->create([
      'user_id' => auth()->id(),
      'description' => $request->description,
      'images' => json_encode($images),
      'attachments' => json_encode($attachments),
    ]);

    // Get all recipients for the notification
    $recipients = collect();

    // Add quotation assignees
    if ($runningJob->quotationAssignees && $runningJob->quotationAssignees->count() > 0) {
      $recipients = $recipients->merge($runningJob->quotationAssignees);
    }

    // Add booking assignee if exists
    if ($runningJob->booking && $runningJob->booking->assignee) {
      $recipients->push($runningJob->booking->assignee);
    }

    // Remove duplicates and the current user
    $recipients = $recipients->unique('id')->filter(function ($user) {
      return $user->id !== auth()->id();
    });

    // Send email to all recipients
    if ($recipients->count() > 0) {
      $senderName = auth()->user()->name;
      foreach ($recipients as $recipient) {
        if ($recipient->email) {
          \Mail::to($recipient->email)->queue(
            new \App\Mail\ConversationNotification($conversation, $runningJob, $senderName)
          );
        }
      }
    }

    return redirect()->back()->with('success', 'Message sent successfully.');
  }


  public function history($id)
  {
    $runningJob = RunningJob::with(['booking', 'conversations.user', 'quotation', 'quotationAssignees'])->findOrFail($id);

    // Get users for different dropdowns
    $usersAssign = User::role(['Super Admin', 'Admin', 'Travel Consultant'])
        ->select('id', 'name')
        ->get();

    $usersHandler = User::whereDoesntHave('roles', function($query) {
        $query->where('name', 'Customer');
    })->select('id', 'name')->get();

    return view('running_jobs.chat', compact('runningJob', 'usersAssign', 'usersHandler'));
  }

  public function getRunningJobs(Request $request)
  {
    if ($request->ajax()) {
      $searchValue = $request->input('search.value');
      $orderColumnIndex = $request->input('order.0.column');
      $orderDirection = $request->input('order.0.dir');
      $columns = $request->input('columns');
      $orderColumn = $columns[$orderColumnIndex]['data'];

      // Get the authenticated user
      $user = auth()->user();

      // Query running jobs with search and pagination
      $runningJobs = RunningJob::with(['handler', 'quotation'])
        ->when(!$user->hasRole(['Super Admin', 'Admin', 'Accountant']), function ($query) use ($user) {
          return $query->where('handler_id', $user->id);
        })
        ->when($searchValue, function ($query) use ($searchValue) {
          return $query->where('description', 'like', "%{$searchValue}%")
            ->orWhereHas('handler', function ($q) use ($searchValue) {
              $q->where('name', 'like', "%{$searchValue}%");
            });
        })
        ->orderBy('created_at', 'desc')
        ->orderBy($orderColumn, $orderDirection)

        ->paginate($request->input('length'), ['*'], 'page', $request->input('start') / $request->input('length') + 1);

      // Prepare the data for DataTables
      $runningJobsData = $runningJobs->map(function ($job) {
        return [
          'id' => $job->id,
          'name' => $job->name,
          'description' => $job->description, // Convert new lines to <br>
          'handler' => $job->handler->name ?? 'N/A',
          'quotation' => $job->quotation->id ?? 'N/A',
        ];
      });

      // Return the JSON response in DataTables format
      return response()->json([
        'draw' => intval($request->input('draw')),
        'recordsTotal' => $runningJobs->total(),
        'recordsFiltered' => $runningJobs->total(),
        'data' => $runningJobsData,
      ]);
    }
  }

  public function updateQuotation(Request $request, $id)
  {
    $request->validate([
      'quotation_number' => 'required|string|max:255',
    ]);

    $runningJob = RunningJob::findOrFail($id);
    $runningJob->quotation_id = $request->quotation_number;
    $runningJob->save();

    return response()->json(['success' => 'Quotation number updated successfully']);
  }

  public function updateQuotationAssignees(Request $request, $id)
  {
    try {
        $runningJob = RunningJob::findOrFail($id);

        $userIds = $request->input('user_ids', []);

        // Sync the quotation assignees
        $runningJob->quotationAssignees()->sync($userIds);

        return response()->json([
            'success' => true,
            'message' => 'Quotation assignees updated successfully.'
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'message' => $e->getMessage()
        ], 500);
    }
  }
}
