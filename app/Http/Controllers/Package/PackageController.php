<?php

namespace App\Http\Controllers\Package;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Template;
use App\Models\Place;
use App\Services\QuotationManageService;

class PackageController extends Controller
{
  public function index(Request $request)
  {
    $language = $request->query('language', app()->getLocale());

    $templates = Template::with([
      'quotation.days',             // Load days to collect places
    ])
      ->where("show", "=", 1)
      ->get()
      ->map(function ($template) {
        // Check if quotation exists before accessing its properties
        if ($template->quotation) {
          // Gather unique places from each day
          $allPlaces = $template->quotation->days
            ->flatMap(function ($day) {
              return [
                $day->startPlace->name ?? null,
                $day->endPlace->name ?? null
              ];
            })
            ->filter() // Remove any null values
            ->unique() // Ensure unique places
            ->values(); // Reset array keys

          return [
            'template_id' => $template->id,
            'template_name' => $template->template_name,
            'quotation_id' => $template->quotation->id,
            'days_count' => $template->quotation->days->count(),
            'all_places' => $allPlaces, // Join places into a single string
            'image' => (new \App\Models\Template())->getImageUrl($template->image),
            'pp_price' => $template->pp_price,
          ];
        }
      });

    $locations = Place::where('country', 62)->pluck('name', "id");

    $dayCounts = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14];

    return view('frontend.packages.index', compact('templates', 'locations', 'dayCounts', 'language'));
  }

  public function filter(Request $request)
  {
    // Start with the base query
    $query = Template::with([
      'quotation.days',
      'quotation.arrivalPlace',
      'quotation.departurePlace',
    ])->where("show", "=", 1);

    // Filter by locations
    if ($request->locations) {
      $query->whereHas('quotation.days', function ($q) use ($request) {
        $q->whereHas('startPlace', function ($placeQuery) use ($request) {
          $placeQuery->whereIn('id', $request->locations);
        })->orWhereHas('endPlace', function ($placeQuery) use ($request) {
          $placeQuery->whereIn('id', $request->locations);
        });
      });
    }

    // Filter by day count
    if ($request->day_count) {
      $query->whereHas('quotation.days', function ($q) use ($request) {
        $q->havingRaw('COUNT(*) = ?', [$request->day_count]);
      });
    }

    // Filter by max price
    if ($request->max_price) {
      $query->where('pp_price', '<=', $request->max_price);
    }

    // Execute the query and map the results
    $filteredItems = $query->get()
      ->map(function ($template) {
        // Gather unique places from days
        $allPlaces = $template->quotation->days
          ->flatMap(function ($day) {
            return [
              $day->startPlace->name ?? null,
              $day->endPlace->name ?? null,
            ];
          })
          ->filter() // Remove nulls
          ->unique() // Get unique places
          ->values(); // Reset array keys

        return [
          'template_id' => $template->id,
          'template_name' => $template->template_name,
          'quotation_id' => $template->quotation->id,
          'days_count' => $template->quotation->days->count(),
          'all_places' => $allPlaces,
          'image' => (new \App\Models\Template())->getImageUrl($template->image),
          'pp_price' => $template->pp_price,
        ];
      });

    // Render the filtered results into a partial view
    $html = view('frontend.packages.package-filter', ['templates' => $filteredItems])->render();

    return response()->json(['html' => $html]);
  }

  public function show($id)
  {
    $quotationManageService = new QuotationManageService();
    $template = Template::with([
      'quotation.days.startPlace', // Load start places for each day
      'quotation.days.endPlace'   // Load end places for each day
    ])->findOrFail($id);

    $quotation = $quotationManageService->reviewQuotation($template->quotation->id,null);

    // Gather unique places from the template's quotation days
    $allPlaces = $template->quotation->days
      ->flatMap(function ($day) {
        return [
          $day->startPlace->name ?? null,
          $day->endPlace->name ?? null
        ];
      })
      ->filter() // Remove any null values
      ->unique() // Ensure unique places
      ->values(); // Reset array keys

    // Format template data
    $templateDetails = [
      'template_id' => $template->id,
      'template_name' => $template->template_name,
      'quotation_id' => $template->quotation->id,
      'days_count' => $template->quotation->days->count(),
      'all_places' => $allPlaces,
      'image' => (new \App\Models\Template())->getImageUrl($template->image),
      'pp_price' => $template->pp_price,
      'quotation' => $quotation,
    ];

    return view('frontend.packages.show', compact('templateDetails'));
  }

  public function form_1()
  {
    return view('frontend.packages.form_1');
  }

  public function form_2()
  {
    return view('frontend.packages.form_2');
  }
}
