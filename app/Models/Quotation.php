<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\SoftDeletes;

class Quotation extends Model
{
  use SoftDeletes;
  protected $fillable = [
    'country',
    'quotation_no',
    'reference_no',
    'client_name',
    'client_country',
    'arrival_date',
    'departure_date',
    'market',
    'adults',
    'children',
    'cwb',
    'cnb',
    'infants',
    'arrival_place',
    'departure_place',
    'vehicle_id',
    'adult_markup',
    'child_markup',
    'single_markup',
    'double_markup',
    'triple_markup',
    'quadruple_markup',
    'markup_type',
    'discount_type',
    'discount',
    'save_type',
    'currency',
    'language',
    'token',
    'user_id',
    'season_id',
    'total',
    'disabled',
  ];

  protected $casts = [
    'disabled' => 'boolean',
  ];

  // Relationship with QuotationChildrenAges
  public function childrenAges()
  {
    return $this->hasMany(QuotationChildrenAge::class, 'quotation_id');
  }

  // Relationship with QuotationChildrenAges
  public function summeryPolicy()
  {
    return $this->hasMany(QuotationSummary::class, 'quotation_id');
  }

  // Relationship with QuotationDays
  public function days()
  {
    return $this->hasMany(QuotationDay::class, 'quotation_id');
  }

  // Relationship with QuotationDays
  public function user()
  {
    return $this->belongsTo(User::class, 'user_id');
  }

  // Relationship with Customer (client_name stores customer user ID)
  public function customer()
  {
    return $this->belongsTo(User::class, 'client_name');
  }

  // Relationships with Arrival and Departure places
  public function arrivalPlace()
  {
    return $this->belongsTo(Place::class, 'arrival_place');
  }

  public function departurePlace()
  {
    return $this->belongsTo(Place::class, 'departure_place');
  }

  public function generateToken()
  {
    // Generate a unique 64-character token
    $this->token = Str::random(64);
    $this->save();
  }

  public function hotels()
  {
    return $this->hasMany(QuotationHotel::class, 'quotation_id');
  }

  public function runningJob()
  {
    return $this->hasOne(RunningJob::class, 'quotation_id', 'quotation_no');
  }

  // Relationship with QuotationComments
  public function comments()
  {
    return $this->hasMany(QuotationComment::class, 'quotation_id', 'quotation_no');
  }

  public function durations()
  {
      return $this->hasMany(QuotationDuration::class);
  }

  public function supplements()
  {
      return $this->hasOne(QuotationSupplement::class);
  }


  public function memberAllocate()
  {
      return $this->hasOne(MemberAllocate::class,'quotation_id', 'quotation_no');
  }

  // New relationship to get all versions (including soft deleted ones) with same quotation_no
  public function versions()
  {
    return $this->hasMany(Quotation::class, 'quotation_no', 'quotation_no')
        ->withTrashed()
        ->orderByDesc('id');
  }
}
