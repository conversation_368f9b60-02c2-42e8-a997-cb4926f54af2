<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\HasImage;

class Attraction extends Model
{
    use HasFactory, HasImage;

    // The table associated with the model
    protected $table = 'attraction';

    // The primary key for the table
    protected $primaryKey = 'id';

    // Indicates if the IDs are auto-incrementing
    public $incrementing = true;

    // The attributes that are mass assignable
    protected $fillable = [
        'place',
        'duration',
        'address',
        'description',
        'es_description',
        'fr_description',
        'it_description',
        'point',
        'name',
        'es_name',
        'fr_name',
        'it_name',
        'prefered',
        'opening',
        'closing',
        'distance',
        'time',
        'longitude',
        'latitude',
        'type',
        'disabled',
        'created_at',
        'email',
        'phone', 'image'
    ];

    // The attributes excluded from the model's JSON form
    protected $hidden = [];

    // Disable timestamps (since the table doesn't have `updated_at`)
    public $timestamps = false;

    public function placeR()
    {
        return $this->belongsTo(Place::class, 'place', 'id');
    }

    public function typeR()
    {
        return $this->belongsTo(AttractionType::class, 'type', 'id');
    }

    public function attractionTypes()
    {
        return $this->belongsToMany(AttractionType::class, 'attraction_attraction_type');
    }

    public function attractionRates()
    {
        return $this->hasMany(AttractionRate::class, 'attraction', 'id');
    }

    public function deleteImage($imagePath)
    {
        if ($imagePath && file_exists(public_path('uploads/attractions/' . $imagePath))) {
            unlink(public_path('uploads/attractions/' . $imagePath));
        }
    }
}
