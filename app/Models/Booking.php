<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Booking extends Model
{
    use HasFactory;

    // Fields that are mass assignable
    protected $fillable = [
        'message',
        'assignee_id',
        'template_id', // Foreign key for Template model
        'status_01',
        'status_02',
        'status_02_comment',
        'status_03',
        'reference_id',
        'air_ticket_files',
        'passport_files',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'air_ticket_files' => 'array',
        'passport_files' => 'array',
    ];

    /**
     * Relationship with the Template model
     */
    public function template()
    {
        return $this->belongsTo(Template::class);
    }

    /**
     * Relationship with the User model
     */
    public function assignee()
    {
        return $this->belongsTo(User::class, 'assignee_id');
    }

    /**
     * Relationship with the RunningJob model
     */
    public function runningJob()
    {
        return $this->hasOne(RunningJob::class, 'booking_id');
    }
}
