<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AttractionType extends Model
{
    use HasFactory;

    // The table associated with the model
    protected $table = 'attraction_types';

    // The primary key for the table
    protected $primaryKey = 'id';

    // Indicates if the IDs are auto-incrementing
    public $incrementing = true;

    // The attributes that are mass assignable
    protected $fillable = [
        'type',
        'created_at',
        'updated_at',
    ];

    // Enable timestamps (since the table has `created_at` and `updated_at`)
    public $timestamps = true;

    // Set the unique rule for 'type' field
    protected $unique = [
        'type',
    ];

    public function attractions()
    {
        return $this->belongsToMany(Attraction::class, 'attraction_attraction_type');
    }
}
