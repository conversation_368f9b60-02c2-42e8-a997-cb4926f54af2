<?php

namespace App\Services;

use App\Models\Quotation;
use App\Models\QuotationChildrenAge;
use App\Models\QuotationDay;
use App\Models\QuotationDayHotel;
use App\Models\QuotationDayAttraction;
use App\Models\QuotationDayMeal;
use App\Models\QuotationDayMember;
use App\Models\QuotationDayTransport;
use App\Models\QuotationDayOther;
use App\Models\QuotationHotel;
use App\Models\QuotationSummary;
use App\Models\QuotationDuration;
use App\Models\QuotationComment;
use App\Models\QuotationDayAdminFee;
use App\Models\QuotationDayExtraPlace;
use App\Models\QuotationSupplement;
use App\Models\RunningJob;

class QuotationManageService
{
  public function saveQuotation($data)
  {
    // Check if it's a new quotation or a re-quote
    /*if (isset($data['quotation_no'])) {
      // Fetch the existing quotation for updating
      $quotation = Quotation::find($data['quotation_no']);

      if (!$quotation) {
        // Handle case where quotation does not exist (optional)
        throw new \Exception('Quotation not found.');
      }

      // Step 1: Update the main quotation details
      $quotation->update([
        'country' => $data['country'],
        'reference_no' => $data['reference_no'],
        'client_name' => $data['client_name'],
        'client_country' => $data['client_country'],
        'arrival_date' => $data['arrival_date'],
        'departure_date' => $data['departure_date'],
        'market' => $data['market'],
        'adults' => $data['adults'],
        'children' => $data['children'],
        'cwb' => $data['cwb'],
        'cnb' => $data['cnb'],
        'infants' => $data['infants'],
        'arrival_place' => $data['arrival_place'],
        'departure_place' => $data['departure_place'],
        'vehicle_id' => $data['vehicle']['id'],
        'adult_markup' => $data['adult_markup'],
        'child_markup' => $data['child_markup'],
        'markup_type' => $data['markup_type'],
        'discount_type' => $data['discount_type'],
        'discount' => $data['discount'],
        'save_type' => $data['save_type'],
        'currency' => $data['currency'],
        'language' => $data['language'],
        'user_id' => $data['user']->id,
        'total' => $data['total']['discounted_total']
      ]);

      // Step 2: Delete existing children ages and save the new ones
      $quotation->childrenAges()->delete();
      foreach ($data['children_ages'] ?? [] as $age) {
        QuotationChildrenAge::create([
          'quotation_id' => $quotation->id,
          'age' => $age,
        ]);
      }

      // Step : Delete existing summery policy and save the new ones
      $quotation->summeryPolicy()->delete();
      foreach ($data['summery']['policy'] ?? [] as $key => $updated_list) {
        QuotationSummary::create([
          'quotation_id' => $quotation->id,
          'ul_id' => $key,
          'updated_list' => $updated_list,
        ]);
      }

      // Save new durations
      $quotation->durations()->delete();
      foreach ($data['summery']['duration'] ?? [] as $day => $duration) {
          QuotationDuration::create([
              'quotation_id' => $quotation->id,
              'day_id' => $day,
              'duration' => $duration,
          ]);
      }

      $quotation->comments()->delete();
      foreach ($data['summery']['comments'] ?? [] as $index => $comment) {
        QuotationComment::create([
          'quotation_id' => $quotation->id,
          'user_id' => $comment['user']->id ?? null,
          'comment_id' => (int)$index,
          'comment' => $comment['text'],
          'created_at' => $comment['created_at'],
        ]);
      }

      if(isset($data['summery']['supplements'])) {
        $quotation->supplements()->delete();
        QuotationSupplement::create([
          'quotation_id' => $quotation->id,
          'updated_list' => $data['summery']['supplements'] ?? null,
        ]);
      }

      // Step 3: Delete and update the days and related data
      $quotation->days()->delete();
    } else { */
      // Create a new quotation
      if (isset($data['quotation_no'])) {
          Quotation::where('quotation_no', $data['quotation_no'])->delete();
      }
      // Create a new quotation
      $quotation = Quotation::create([
        'country' => $data['country'],
        'reference_no' => $data['reference_no'],
        'client_name' => $data['client_name'],
        'client_country' => $data['client_country'],
        'arrival_date' => $data['arrival_date'],
        'departure_date' => $data['departure_date'],
        'market' => $data['market'],
        'adults' => $data['adults'],
        'children' => $data['children'],
        'cwb' => $data['cwb'],
        'cnb' => $data['cnb'],
        'infants' => $data['infants'],
        'arrival_place' => $data['arrival_place'],
        'departure_place' => $data['departure_place'],
        'vehicle_id' => $data['vehicle']['id'],
        'adult_markup' => $data['adult_markup'],
        'child_markup' => $data['child_markup'],
        'markup_type' => $data['markup_type'],
        'discount_type' => $data['discount_type'],
        'discount' => $data['discount'],
        'single_markup' => $data['single_markup'] ?? null,
        'double_markup' => $data['double_markup'] ?? null,
        'triple_markup' => $data['triple_markup'] ?? null,
        'quadruple_markup' => $data['quadruple_markup'] ?? null,
        'save_type' => $data['save_type']??"save",
        'currency' => $data['currency'],
        'language' => $data['language'],
        'user_id' => $data['user']->id ?? $data['user']['id'],
        'season_id' => $data['season_id'],
        'total' => $data['total']['discounted_total']
      ]);

      if (isset($data['quotation_no'])) {
          $quotation->quotation_no = $data['quotation_no'];
      } else {
          $quotation->quotation_no = $quotation->id;
      }
      $quotation->save();

      // Step 2: Save children ages
      foreach ($data['children_ages'] ?? [] as $age) {
        QuotationChildrenAge::create([
          'quotation_id' => $quotation->id,
          'age' => $age,
        ]);
      }

      // Step : Save the new ones
      foreach ($data['summery']['policy'] ?? [] as $key => $updated_list) {
        QuotationSummary::create([
          'quotation_id' => $quotation->id,
          'ul_id' => $key,
          'updated_list' => $updated_list,
        ]);
      }

      foreach ($data['summery']['duration'] ?? [] as $day => $duration) {
          QuotationDuration::create([
              'quotation_id' => $quotation->id,
              'day_id' => $day,
              'duration' => $duration,
          ]);
      }

      // Step : Save the new ones
      foreach ($data['summery']['comments'] ?? [] as $index => $comment) {
        QuotationComment::create([
          'quotation_id' => $quotation->id,
          'user_id' => $comment['user']->id,
          'comment_id' => (int)$index,
          'comment' => $comment['text'],
          'created_at' => $comment['created_at'],
        ]);
      }

      if(isset($data['summery']['supplements'])) {
        QuotationSupplement::create([
          'quotation_id' => $quotation->id,
          'updated_list' => $data['summery']['supplements'] ?? null,
        ]);
      }
    //}

    $quotation->generateToken();

    if (isset($data['save_type']) &&$data['save_type'] == 'confirm') {
      //$hotelList = HotelService::getHotelsList($data);
    }
    // Step 3: Save each day
    foreach ($data['days'] as $dayData) {
      $quotationDay = QuotationDay::create([
        'quotation_id' => $quotation->id,
        'day' => $dayData['day'],
        'date' => $dayData['date'],
        'start' => $dayData['start'],
        'end' => $dayData['end'],
        'data_type' => $dayData['data_type'],
      ]);

      // Step 4: Save day hotels
      $hotelData = $dayData['hotels'];
      QuotationDayHotel::create([
        'quotation_day_id' => $quotationDay->id,
        'hotel_id' => $hotelData['hotel-id'],
        'own_arrangement' => $hotelData['own-arrangement'],
        'nights' => $hotelData['nights'],
        'room_category' => $hotelData['room_category'],
        'meal' => $hotelData['meal'],
        'single' => $hotelData['single'],
        'double' => $hotelData['double'],
        'triple' => $hotelData['triple'],
        'quadruple' => $hotelData['quadruple'],
        'adult_rate_single' => $hotelData['adult_rate[1]'] ?? 0,
        'adult_rate_double' => $hotelData['adult_rate[2]'] ?? 0,
        'adult_rate_triple' => $hotelData['adult_rate[3]'] ?? 0,
        'adult_rate_quadruple' => $hotelData['adult_rate[4]'] ?? 0,
        'cwb_rate' => $hotelData['cwb_rate'],
        'cnb_rate' => $hotelData['cnb_rate'],
        'discounted_adult_rate_single' => $hotelData['discounted_adult_rate[1]'] ?? null,
        'discounted_adult_rate_double' => $hotelData['discounted_adult_rate[2]'] ?? null,
        'discounted_adult_rate_triple' => $hotelData['discounted_adult_rate[3]'] ?? null,
        'discounted_adult_rate_quadruple' => $hotelData['discounted_adult_rate[4]'] ?? null,
        'discounted_cwb_rate' => $hotelData['discounted_cwb_rate'] ?? null,
        'discounted_cnb_rate' => $hotelData['discounted_cnb_rate'] ?? null,
        'discount_title' => $hotelData['discount_title'] ?? 'Discount given by Hotel',
      ]);

      // Step 5: Save day extra_places
      foreach ($dayData['extra_places'] ?? [] as $key => $extra_place) {
        QuotationDayExtraPlace::create([
          'quotation_day_id' => $quotationDay->id,
          'place_id' => $extra_place,
          'index' => $key
        ]);
      }

      // Step 6: Save day attractions
      foreach ($dayData['attractions'] as $attraction) {
        QuotationDayAttraction::create([
          'quotation_day_id' => $quotationDay->id,
          'attraction_id' => $attraction['attraction-id'],
          'adult_rate' => $attraction['adult_rate'],
          'child_rate' => $attraction['child_rate'],
          'attraction_type' => $attraction['attraction_type'],
          'distance' => $attraction['distance'],
        ]);
      }

      // Step 7: Save day meals
      QuotationDayMeal::create([
        'quotation_day_id' => $quotationDay->id,
        'breakfast' => $dayData['meals']['breakfast'],
        'lunch' => $dayData['meals']['lunch'],
        'dinner' => $dayData['meals']['dinner'],
      ]);

      // Step 8: Save day transport
      QuotationDayTransport::create([
        'quotation_day_id' => $quotationDay->id,
        'distance' => $dayData['transports']['distance'],
        'rate' => $dayData['transports']['rate'],
      ]);

      // Step 9: Save day members
      foreach ($dayData['members'] ?? [] as $members) {
        QuotationDayMember::create([
          'quotation_day_id' => $quotationDay->id,
          'language_id' => $members['language_id'],
          'position_id' => $members['position_id'],
          'type_id' => $members['type_id'],
          'member_rate' => $members['member_rate'],
          'member_id' => $members['id'],
        ]);
      }

      // Step 10: Save day admin_fees
      foreach ($dayData['admin_fees'] ?? [] as $admin_fees) {
        QuotationDayAdminFee::create([
          'quotation_day_id' => $quotationDay->id,
          'duration_id' => $admin_fees['duration_id'],
          'category_id' => $admin_fees['category_id'],
          'pax_id' => $admin_fees['pax_id'],
          'admin_fee_rate' => $admin_fees['admin_fee_rate'],
          'admin_fee_id' => $admin_fees['id'],
        ]);
      }

      // Step 11: Save day members
      foreach ($dayData['others'] ?? [] as $index => $others) {
        QuotationDayOther::create([
          'quotation_day_id' => $quotationDay->id,
          'index' => $index,
          'other_title' => $others['other_title'],
          'other_rate' => $others['other_rate'],
        ]);
      }
    }

    // After saving the quotation, update the running job if needed
    if (isset($data['running_job_id'])) {
      $runningJob = RunningJob::find($data['running_job_id']);
      if ($runningJob) {
        $runningJob->quotation_id = $quotation->id;
        $runningJob->save();
      }
    }

    return $quotation;
  }


  public function reviewQuotation($id, $quotation_no)
  {
    // Step 1: Fetch the main quotation details
    if(isset($id)) {
      $quotation = Quotation::with([
        'childrenAges',
        'user',
        'days.hotels',
        'days.attractions',
        'days.meals',
        'days.transports',
        'days.members',
        'days.admin_fees',
        'days.others'
      ])->withTrashed()->findOrFail($id);
    } else {
      $quotation = Quotation::with([
        'childrenAges',
        'user',
        'days.hotels',
        'days.attractions',
        'days.meals',
        'days.transports',
        'days.members',
        'days.admin_fees',
        'days.others'
      ])->where('quotation_no', $quotation_no)->firstOrFail();
    }

    // If the quotation is not found, handle it gracefully
    if (!$quotation) {
      return response()->json(['error' => 'Quotation not found.'], 404);
    }

    // Step 2: Format the data for response
    $quotationData = [
      'id' => $quotation->id,
      'quotation_no' => $quotation->quotation_no,
      'reference_no' => $quotation->reference_no,
      'client_name' => $quotation->client_name,
      'client_country' => $quotation->client_country,
      'country' => $quotation->country,
      'arrival_date' => $quotation->arrival_date,
      'departure_date' => $quotation->departure_date,
      'market' => $quotation->market,
      'adults' => $quotation->adults,
      'children' => $quotation->children,
      'cwb' => $quotation->cwb,
      'cnb' => $quotation->cnb,
      'infants' => $quotation->infants,
      'arrival_place' => $quotation->arrival_place,
      'departure_place' => $quotation->departure_place,
      'vehicle' => [
        'id' => $quotation->vehicle_id,
        'name' => optional($quotation->vehicle)->name,
      ],
      'children_ages' => $quotation->childrenAges->pluck('age')->toArray(),
      'adult_markup' => $quotation->adult_markup,
      'child_markup' => $quotation->child_markup,
      'markup_type' => $quotation->markup_type,
      'save_type' => $quotation->save_type,
      'discount_type' => $quotation->discount_type,
      'discount' => $quotation->discount,
      'single_markup' => $quotation->single_markup,
      'double_markup' => $quotation->double_markup,
      'triple_markup' => $quotation->triple_markup,
      'quadruple_markup' => $quotation->quadruple_markup,
      'currency' => $quotation->currency,
      'language' => $quotation->language,
      'token' => $quotation->token,
      'user' => $quotation->user,
      'season_id' => $quotation->season_id ?? 2,
      'days' => [],
      'created_at' => $quotation->created_at,
      'summery' => [
        'policy' => $quotation->summeryPolicy->pluck('updated_list', 'ul_id')->toArray(),
        'duration' => $quotation->durations->pluck('duration', 'day_id')->toArray(),
        'comments' => $quotation->comments->map(function ($comment) {
          return [
            'comment_id' => $comment->comment_id,
            'user' => $comment->user ?? [],
            'text' => $comment->comment,
            'created_at' => $comment->created_at,
          ];
        })->toArray(),
        'supplements' => optional(optional($quotation->supplements))->updated_list,
      ],
      'source_type' => 'update'
    ];

    // Step 3: Loop through days and format the related data
    foreach ($quotation->days as $day) {
      $dayData = [
        'day' => $day->day,
        'date' => $day->date,
        'start' => $day->start,
        'end' => $day->end,
        'data_type' => "permanent",
        'hotels' => [
          'hotel-id' => $day->hotels->hotel_id,
          'own-arrangement' => $day->hotels->own_arrangement,
          'nights' => $day->hotels->nights,
          'room_category' => $day->hotels->room_category,
          'meal' => $day->hotels->meal,
          'single' => $day->hotels->single,
          'double' => $day->hotels->double,
          'triple' => $day->hotels->triple,
          'quadruple' => $day->hotels->quadruple,
          'adult_rate[1]' => $day->hotels->adult_rate_single,
          'adult_rate[2]' => $day->hotels->adult_rate_double,
          'adult_rate[3]' => $day->hotels->adult_rate_triple,
          'adult_rate[4]' => $day->hotels->adult_rate_quadruple,
          'cwb_rate' => $day->hotels->cwb_rate,
          'cnb_rate' => $day->hotels->cnb_rate,
          'discounted_adult_rate[1]' => $day->hotels->discounted_adult_rate_single,
          'discounted_adult_rate[2]' => $day->hotels->discounted_adult_rate_double,
          'discounted_adult_rate[3]' => $day->hotels->discounted_adult_rate_triple,
          'discounted_adult_rate[4]' => $day->hotels->discounted_adult_rate_quadruple,
          'discounted_cwb_rate' => $day->hotels->discounted_cwb_rate,
          'discounted_cnb_rate' => $day->hotels->discounted_cnb_rate,
          'discount_title' => $day->hotels->discount_title,
        ],
        'attractions' => $day->attractions->map(function ($attraction) {
          return [
            'attraction-id' => $attraction->attraction_id,
            'adult_rate' => $attraction->adult_rate,
            'child_rate' => $attraction->child_rate,
            'attraction_type' => $attraction->attraction_type,
            'distance' => $attraction->distance,
          ];
        })->toArray(),
        'extra_places' => $day->extraPlaces->map(function ($extraPlace) {
            return $extraPlace->place_id;
        })->values()->toArray(),
        'meals' => [
          'breakfast' => $day->meals->breakfast,
          'lunch' => $day->meals->lunch,
          'dinner' => $day->meals->dinner,
        ],
        'transports' => [
          'bata' => $day->transports->bata ?? 0,
          'distance' => $day->transports->distance ?? 0,
          'driver_accommodation' => $day->transports->driver_accommodation ?? 0,
          'highway_charges' => $day->transports->highway_charges ?? 0,
          'rate' => $day->transports->rate ?? 0,
        ],
        'members' => $day->members->map(function ($member) {
          return [
            'language_id' => $member->language_id,
            'position_id' => $member->position_id,
            'type_id' => $member->type_id,
            'id' => $member->member_id,
            'member_rate' => $member->member_rate,
          ];
        })->toArray(),
        'admin_fees' => $day->admin_fees->map(function ($admin_fee) {
          return [
            'duration_id' => $admin_fee->duration_id,
            'category_id' => $admin_fee->category_id,
            'pax_id' => $admin_fee->pax_id,
            'id' => $admin_fee->admin_fee_id,
            'admin_fee_rate' => $admin_fee->admin_fee_rate,
          ];
        })->toArray(),
        'others' => $day->others->map(function ($other, $index) {
          return [
            'index' => $other->index,
            'other_title' => $other->other_title,
            'other_rate' => $other->other_rate
          ];
        })->toArray(),
      ];

      $quotationData['days'][$day->day] = $dayData;
    }

    return $quotationData;
  }


  public function deleteQuotation($id)
  {
    // Step 1: Find the quotation
    $quotation = Quotation::find($id);

    // If the quotation is not found, return an error response
    if (!$quotation) {
      return response()->json(['error' => 'Quotation not found.'], 404);
    }

    try {
      // Step 2: Delete related data first (if necessary)
      $quotation->childrenAges()->delete();
      $quotation->days()->each(function ($day) {
        $day->hotels()->delete();
        $day->attractions()->delete();
        $day->meals()->delete();
        $day->transports()->delete();
        $day->members()->delete();
        $day->admin_fees()->delete();
        $day->others()->delete();
        $day->delete();
      });

      // Step 3: Delete the quotation itself
      $quotation->delete();

      // Step 4: Return success response
      return response()->json(['success' => 'Quotation deleted successfully.'], 200);
    } catch (\Exception $e) {
      // Handle exceptions
      return response()->json(['error' => 'Failed to delete quotation: ' . $e->getMessage()], 500);
    }
  }


  public function pullTemplate($id)
  {
    // Step 1: Fetch the main quotation details
    $quotation = Quotation::with([
      'childrenAges',
      'user',
      'days.hotels',
      'days.attractions',
      'days.meals',
      'days.transports',
      'days.members',
      'days.admin_fees',
      'days.others'
    ])->findOrFail($id);

    // If the quotation is not found, handle it gracefully
    if (!$quotation) {
      return response()->json(['error' => 'Quotation not found.'], 404);
    }

    // Step 2: Format the data for response
    $quotationData = [
      'country' => $quotation->country,
      'reference_no' => $quotation->reference_no,
      'arrival_date' => null,
      'departure_date' => null,
      'market' => $quotation->market,
      'adults' => 1,
      'children' => 0,
      'cwb' => 0,
      'cnb' => 0,
      'infants' => 0,
      'arrival_place' => $quotation->arrival_place,
      'departure_place' => $quotation->departure_place,
      'vehicle' => [
        'id' => 0,
        'name' => null,
      ],
      'children_ages' => [],
      'adult_markup' => $quotation->adult_markup,
      'child_markup' => $quotation->child_markup,
      'markup_type' => $quotation->markup_type,
      'single_markup' => $quotation->single_markup,
      'double_markup' => $quotation->double_markup,
      'triple_markup' => $quotation->triple_markup,
      'quadruple_markup' => $quotation->quadruple_markup,
      'currency' => $quotation->currency,
      'language' => $quotation->language,
      'days' => [],
      'created_at' => $quotation->created_at,
      'summery' => [
        'policy' => $quotation->summeryPolicy->pluck('updated_list', 'ul_id')->toArray(),
        'duration' => $quotation->durations->pluck('duration', 'day_id')->toArray(),
        'comments' => [],
        'supplements' => optional(optional($quotation->supplements))->updated_list,
      ],
      'source_type' => 'template'
    ];

    // Step 3: Loop through days and format the related data
    foreach ($quotation->days as $day) {
      $dayData = [
        'day' => $day->day,
        'date' => null,
        'start' => $day->start,
        'end' => $day->end,
        'data_type' => "permanent",
        'hotels' => [
          'hotel-id' => $day->hotels->hotel_id,
          'own-arrangement' => $day->hotels->own_arrangement,
          'nights' => $day->hotels->nights,
          'room_category' => $day->hotels->room_category,
          'meal' => $day->hotels->meal,
          'single' => null,
          'double' => null,
          'triple' => null,
          'quadruple' => null,
          'adult_rate[1]' => null,
          'adult_rate[2]' => null,
          'adult_rate[3]' => null,
          'adult_rate[4]' => null,
          'cwb_rate' => null,
          'cnb_rate' => null,
        ],
        'attractions' => $day->attractions->map(function ($attraction) {
          return [
            'attraction-id' => $attraction->attraction_id,
            'adult_rate' => null,
            'child_rate' => null,
            'attraction_type' => $attraction->attraction_type,
            'distance' => $attraction->distance,
          ];
        })->toArray(),
        'meals' => [
          'breakfast' => $day->meals->breakfast,
          'lunch' => $day->meals->lunch,
          'dinner' => $day->meals->dinner,
        ],
        'transports' => [
          'bata' => $day->transports->bata ?? 0,
          'distance' => $day->transports->distance ?? 0,
          'driver_accommodation' => $day->transports->driver_accommodation ?? 0,
          'highway_charges' => $day->transports->highway_charges ?? 0,
          'rate' => null,
        ],
        'members' => $day->members->map(function ($member) {
          return [
            'language_id' => $member->language_id,
            'position_id' => $member->position_id,
            'type_id' => $member->type_id,
            'id' => $member->member_id,
            'member_rate' => $member->member_rate,
          ];
        })->toArray(),
        'admin_fees' => $day->admin_fees->map(function ($admin_fee) {
          return [
            'duration_id' => $admin_fee->duration_id,
            'category_id' => $admin_fee->category_id,
            'pax_id' => $admin_fee->pax_id,
            'id' => $admin_fee->admin_fee_id,
            'admin_fee_rate' => $admin_fee->admin_fee_rate,
          ];
        })->toArray(),
        'others' => $day->others->map(function ($other, $index) {
          return [
            'index' => $other->index,
            'other_title' => $other->other_title,
            'other_rate' => $other->other_rate
          ];
        })->toArray(),
      ];

      $quotationData['days'][$day->day] = $dayData;
    }

    return $quotationData;
  }

  public function getHotelReviews($quotation_id)
  {
    // Fetch the quotation with its hotels and related details
    $quotation = Quotation::with(['hotels'])->findOrFail($quotation_id);
    // Prepare data for the view or API
    $hotelReviews = $quotation->hotels->mapWithKeys(function ($hotel) {
      return [
        $hotel->hotel_id => $hotel->remarks ?? 'No remarks provided',
      ];
    });

    return $hotelReviews;
  }
}
