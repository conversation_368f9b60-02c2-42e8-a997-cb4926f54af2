<?php

namespace App\Services;

use App\Http\Controllers\Attraction\AttractionController;
use DateTime;
use DateInterval;
use DatePeriod;
use App\Models\Setting;
use Carbon\Carbon;

class QuotationService
{
  public static function getDatesBetween($quotation)
  {
    $start = new DateTime($quotation['arrival_date']);
    $end = new DateTime($quotation['departure_date']);
    $end->modify('+1 day');
    $dates = [];
    $interval = new DateInterval('P1D');
    $datePeriod = new DatePeriod($start, $interval, $end);

    foreach ($datePeriod as $index => $date) {
      $dates[$index + 1] = ['date' => $date->format('Y-m-d'), 'day' => $index + 1];
    }

    return $dates;
  }

  public function getDateByDayOffset(string $arrivalDate, int $dayOffset): string
  {
    // Use Carbon to parse and manipulate dates
    $date = Carbon::parse($arrivalDate);

    // Add the day offset to the arrival_date
    $newDate = $date->addDays($dayOffset - 1);

    // Return the new date as a string
    return $newDate->toDateString();
  }

  public static function convertMinutesToHours($minutes)
  {
    $hours = floor($minutes / 60); // Calculate the hours
    $remainingMinutes = $minutes % 60; // Calculate the remaining minutes

    return sprintf('%d:%02d Hr', $hours, $remainingMinutes);
  }

  public static function generateQuotationDataStart($quotation)
  {
    session()->put('data.quotation.days.1', [
      "start" => $quotation['arrival_place']
    ]);
  }

  public static function generateQuotationDataContent($block)
  {
    $quotation = session('data.quotation');

    $dates = \App\Services\QuotationService::getDatesBetween($quotation);

    foreach ($dates as $day => $date) {
      if ($day <= $block['day']) continue;

      $obj = [
        'day' => $day,
        'date' => $date['date'],
        'start' => $block['end'],
        'end' => $block['end'],
        'hotels' => $block['hotels'],
        'attractions' => $block['attractions'],
        'extra_places' => $block['extra_places'],
        'meals' => $block['meals'],
        'members' => $block['members'],
        'admin_fees' => $block['admin_fees'],
        'others' => $block['others'],
        'data_type' => 'temporary'
      ];

      if ((session()->has('data.quotation.days.' . $day . '.data_type')) && session('data.quotation.days.' . $day . '.data_type') == "permanent") {
        // dd($day, session('data.quotation.days.' . $day . '.data_type'));
      } else {
        session()->put('data.quotation.days.' . $day, $obj);
      }
    }
  }

  public static function selectVehicle($quotation, $vehicleType)
  {
    $pax = \App\Services\QuotationService::calculatePaxCount($quotation);
    $countryID = $quotation['country'];
    $arrivalDate = $quotation['arrival_date'];

    // Convert the arrival date to the correct format if necessary
    $arrivalDate = \Carbon\Carbon::parse($arrivalDate)->format('Y-m-d');

    $vehicleType = \App\Models\VehicleType::where('id', $vehicleType)
      ->whereHas('vehicleRates', function ($query) use ($pax, $countryID, $arrivalDate) {
        $query->where('country', $countryID)
          ->where('start_date', '<=', $arrivalDate)
          ->where('end_date', '>=', $arrivalDate);
      })
      ->with(['vehicleRates' => function ($query) use ($pax, $countryID, $arrivalDate) {
        $query->where('country', $countryID)
          ->where('start_date', '<=', $arrivalDate)
          ->where('end_date', '>=', $arrivalDate);
      }])
      ->first(); // Get the first matching vehicle type

    if ($vehicleType) {
      return $vehicleType->toArray();
    }

    return false;
  }

  public function getRoadDistanceFromGoogle($latitudeFrom, $longitudeFrom, $latitudeTo, $longitudeTo)
  {
    // Google Maps API key
    $apiKey = Setting::getValue('GOOGLE_MAPS_API_KEY', null); // Store your API key in the .env file

    // Prepare the request URL for the Google Distance Matrix API
    $url = "https://maps.googleapis.com/maps/api/distancematrix/json?origins={$latitudeFrom},{$longitudeFrom}&destinations={$latitudeTo},{$longitudeTo}&key={$apiKey}";

    // Send request to Google Distance Matrix API
    $response = file_get_contents($url);
    $data = json_decode($response, true);

    // Check for errors in response
    if ($data['status'] !== 'OK') {
      return ['error' => 'Failed to get data from Google Maps API'];
    }

    // Extract distance and duration from response
    $distance = $data['rows'][0]['elements'][0]['distance']['value'] ?? null; // e.g. '50 km'
    $duration = $data['rows'][0]['elements'][0]['duration']['text'] ?? null; // e.g. '45 mins'

    return [
      'distance' => $distance,
      'duration' => $duration,
      'raw' => $data // Optionally include raw data for debugging
    ];
  }

  public function getDrivingDistanceWithWaypoints($places)
  {
      $apiKey = Setting::getValue('GOOGLE_MAPS_API_KEY', null);

      if (count($places) < 2) {
          return ['error' => 'At least two places required'];
      }

      // Extract origin and destination
      $origin = "{$places[0]->latitude},{$places[0]->longitude}";
      $destination = "{$places[count($places)-1]->latitude},{$places[count($places)-1]->longitude}";

      // Extract waypoints in between
      $waypoints = [];
      for ($i = 1; $i < count($places) - 1; $i++) {
          $waypoints[] = "{$places[$i]->latitude},{$places[$i]->longitude}";
      }

      $waypointsParam = implode('|', $waypoints);
      $url = "https://maps.googleapis.com/maps/api/directions/json?origin={$origin}&destination={$destination}&key={$apiKey}";

      if (!empty($waypoints)) {
          $url .= "&waypoints=" . urlencode($waypointsParam);
      }

      $response = file_get_contents($url);
      $data = json_decode($response, true);

      if ($data['status'] !== 'OK') {
          return ['error' => 'Failed to get data from Google Maps API'];
      }

      // Sum up all legs
      $totalDistance = 0;
      $totalDuration = 0;
      foreach ($data['routes'][0]['legs'] as $leg) {
          $totalDistance += $leg['distance']['value'];   // in meters
          $totalDuration += $leg['duration']['value'];   // in seconds
      }

      return [
          'distance' => $totalDistance,    // meters
          'duration' => $totalDuration,    // seconds
          'raw' => $data
      ];
  }

  public static function calculatePaxCount($data)
  {
    // Initialize total pax with number of adults
    $paxCount = (int) $data['adults'];

    // Count children with age greater than 2 years
    foreach ($data['children_ages'] ?? [] as $age) {
      if ((int)$age > 3) {
        $paxCount++;
      }
    }

    return $paxCount;
  }

  public static function categorizeGuests($quotation)
  {
    // Age limits
    $infantLimit = [0, 3]; // 0-3 years
    $cnbLimit = [4, 5];    // 3-5 years (CNB: Child No Bed)
    $cwbLimit = [6, 11];   // 6-11 years (CWB: Child with Bed)

    // Initial counts
    $adultsCount = isset($quotation['adults']) ? (int)$quotation['adults'] : 0;
    $infantsCount = 0;
    $cwbCount = 0;
    $cnbCount = 0;

    // Check children's ages if provided
    if (isset($quotation['children_ages']) && is_array($quotation['children_ages'])) {
      foreach ($quotation['children_ages'] as $age) {
        $age = (int)$age; // Ensure it's an integer

        if ($age >= $infantLimit[0] && $age <= $infantLimit[1]) {
          $infantsCount++; // Infant
        } elseif ($age >= $cnbLimit[0] && $age <= $cnbLimit[1]) {
          $cnbCount++; // CNB: Child No Bed
        } elseif ($age >= $cwbLimit[0] && $age <= $cwbLimit[1]) {
          $cwbCount++; // CWB: Child with Bed
        }
      }
    }

    // Return the categorized counts
    return [
      'adults' => $adultsCount,
      'infants' => $infantsCount,
      'cwb' => $cwbCount,
      'cnb' => $cnbCount,
    ];
  }

  public function calculateQuotationRates($quotation, $usdValue = false)
  {
    $quotationService = new QuotationService();
    $hotelService = new HotelService();
    $attractionService = new AttractionService();
    $transportService = new TransportService();
    $adminFeeService = new AdminFeeService();
    $otherService = new OtherService();
    // Calculate hotel rates
    $data['hotel'] = $hotelService->calculateHotelRates($quotation);

    // Calculate attraction rates
    $data['attraction'] = $attractionService->calculateAttractionRates($quotation);

    // Calculate transport rates
    $data['transport'] = $transportService->calculateTransportRates($quotation);

    // Admin Fee rates
    $data['admin_fee'] = $adminFeeService->calculateAdminFeeRates($quotation);

    // Calculate other rates
    $data['other'] = $otherService->calculateOtherRates($quotation);
    // $data = $quotationService->addMarkUp($quotation, $data);

    // Initialize the breakdown values for total
    $data['total'] = [
      'single' => 0,
      'double' => 0,
      'triple' => 0,
      'quadruple' => 0,
      'single_discounted' => 0,
      'double_discounted' => 0,
      'triple_discounted' => 0,
      'quadruple_discounted' => 0,
      'cwb' => 0,
      'cnb' => 0,
      'total' => 0,
      'discount' => 0,
      'discounted_total' => 0
    ];

    // Sum up hotel, attraction, and transport totals
    $data['total']['single'] += $data['hotel']['single'] ?? 0;
    $data['total']['double'] += $data['hotel']['double'] ?? 0;
    $data['total']['triple'] += $data['hotel']['triple'] ?? 0;
    $data['total']['quadruple'] += $data['hotel']['quadruple'] ?? 0;
    $data['total']['cwb'] += $data['hotel']['cwb'] ?? 0;
    $data['total']['cnb'] += $data['hotel']['cnb'] ?? 0;
    $data['total']['total'] += $data['hotel']['total'] ?? 0;

    $data['total']['single'] += $data['attraction']['single'] ?? 0;
    $data['total']['double'] += $data['attraction']['double'] ?? 0;
    $data['total']['triple'] += $data['attraction']['triple'] ?? 0;
    $data['total']['quadruple'] += $data['attraction']['quadruple'] ?? 0;
    $data['total']['cwb'] += $data['attraction']['cwb'] ?? 0;
    $data['total']['cnb'] += $data['attraction']['cnb'] ?? 0;
    $data['total']['total'] += $data['attraction']['total'] ?? 0;

    $data['total']['single'] += $data['transport']['single'] ?? 0;
    $data['total']['double'] += $data['transport']['double'] ?? 0;
    $data['total']['triple'] += $data['transport']['triple'] ?? 0;
    $data['total']['quadruple'] += $data['transport']['quadruple'] ?? 0;
    $data['total']['cwb'] += $data['transport']['cwb'] ?? 0;
    $data['total']['cnb'] += $data['transport']['cnb'] ?? 0;
    $data['total']['total'] += $data['transport']['total'] ?? 0;

    $data['total']['single'] += $data['admin_fee']['single'] ?? 0;
    $data['total']['double'] += $data['admin_fee']['double'] ?? 0;
    $data['total']['triple'] += $data['admin_fee']['triple'] ?? 0;
    $data['total']['quadruple'] += $data['admin_fee']['quadruple'] ?? 0;
    $data['total']['cwb'] += $data['admin_fee']['cwb'] ?? 0;
    $data['total']['cnb'] += $data['admin_fee']['cnb'] ?? 0;
    $data['total']['total'] += $data['admin_fee']['total'] ?? 0;

    $data['total']['single'] += $data['other']['single'] ?? 0;
    $data['total']['double'] += $data['other']['double'] ?? 0;
    $data['total']['triple'] += $data['other']['triple'] ?? 0;
    $data['total']['quadruple'] += $data['other']['quadruple'] ?? 0;
    $data['total']['cwb'] += $data['other']['cwb'] ?? 0;
    $data['total']['cnb'] += $data['other']['cnb'] ?? 0;
    $data['total']['total'] += $data['other']['total'] ?? 0;

    if(isset($quotation['markup_type']) && $quotation['markup_type'] == '2') {
        if($data['total']['single'] > 0)
          $data['total']['single'] = $data['total']['single'] + ($quotation['single_markup'] > 0 ? $quotation['single_markup'] : ($quotation['adult_markup'] > 0 ? $quotation['adult_markup'] : 0));
        if($data['total']['double'] > 0)
          $data['total']['double'] = $data['total']['double'] + ($quotation['double_markup'] > 0 ? $quotation['double_markup'] : ($quotation['adult_markup'] > 0 ? $quotation['adult_markup'] : 0));
        if($data['total']['triple'] > 0)
          $data['total']['triple'] = $data['total']['triple'] + ($quotation['triple_markup'] > 0 ? $quotation['triple_markup'] : ($quotation['adult_markup'] > 0 ? $quotation['adult_markup'] : 0));
        if($data['total']['quadruple'] > 0)
          $data['total']['quadruple'] = $data['total']['quadruple'] + ($quotation['quadruple_markup'] > 0 ? $quotation['quadruple_markup'] : ($quotation['adult_markup'] > 0 ? $quotation['adult_markup'] : 0));
        $data['total']['cwb'] = $data['total']['cwb'] + $quotation['child_markup'];
        $data['total']['cnb'] = $data['total']['cnb'] + $quotation['child_markup'];

        // Calculate total with room-specific markups
        $adultTotal = 0;
        $adultTotal += ($quotation['days'][1]['hotels']['single'] ?? 0) * ($quotation['single_markup'] > 0 ? $quotation['single_markup'] : $quotation['adult_markup']);
        $adultTotal += ($quotation['days'][1]['hotels']['double'] ?? 0) * 2 * ($quotation['double_markup'] > 0 ? $quotation['double_markup'] : $quotation['adult_markup']);
        $adultTotal += ($quotation['days'][1]['hotels']['triple'] ?? 0) * 3 * ($quotation['triple_markup'] > 0 ? $quotation['triple_markup'] : $quotation['adult_markup']);
        $adultTotal += ($quotation['days'][1]['hotels']['quadruple'] ?? 0) * 4 * ($quotation['quadruple_markup'] > 0 ? $quotation['quadruple_markup'] : $quotation['adult_markup']);

        $childTotal = $quotation['child_markup'] * (QuotationService::categorizeGuests($quotation)['cwb'] + QuotationService::categorizeGuests($quotation)['cnb']);

        $data['total']['total'] = $data['total']['total'] + $adultTotal + $childTotal;
    }
    $oldTotal = $data['total']['total'];

    $data['currency_symbol'] = "$";

    $usdToEurRate = Setting::getValue($quotation['currency'], 1.0);
    // DISCOUNT FEATURE

    if($data['total']['single'] > 0)
      $data['total']['single_discounted'] = $data['total']['single'];
    if($data['total']['double'] > 0)
      $data['total']['double_discounted'] = $data['total']['double'];
    if($data['total']['triple'] > 0)
      $data['total']['triple_discounted'] = $data['total']['triple'];
    if($data['total']['quadruple'] > 0)
      $data['total']['quadruple_discounted'] = $data['total']['quadruple'];
    $data['total']['discounted_total'] = $data['total']['total'];

    if (isset($quotation["discount_type"]) && isset($quotation["discount"])) {
      if ($quotation["discount_type"] == "1") {
        // Percentage Discount
        if($data['total']['single'] > 0)
          $data['total']['single_discounted'] = $data['total']['single'] - ($data['total']['single'] * $quotation["discount"] / 100);
        if($data['total']['double'] > 0)
          $data['total']['double_discounted'] = $data['total']['double'] - ($data['total']['double'] * $quotation["discount"] / 100);
        if($data['total']['triple'] > 0)
          $data['total']['triple_discounted'] = $data['total']['triple'] - ($data['total']['triple'] * $quotation["discount"] / 100);
        if($data['total']['quadruple'] > 0)
          $data['total']['quadruple_discounted'] = $data['total']['quadruple'] - ($data['total']['quadruple'] * $quotation["discount"] / 100);

        $data['total']['discounted_total'] = $data['total']['single_discounted'] * ($quotation['days'][1]['hotels']['single']??0 * 1) +
                                    $data['total']['double_discounted'] * ($quotation['days'][1]['hotels']['double']??0 * 2) +
                                    $data['total']['triple_discounted'] * ($quotation['days'][1]['hotels']['triple']??0 * 3) +
                                    $data['total']['quadruple_discounted'] * ($quotation['days'][1]['hotels']['quadruple']??0 * 4) +
                                    $data['total']['cwb'] +
                                    $data['total']['cnb'];

        $data['total']['discount'] = $data['total']['total'] - $data['total']['discounted_total'];
      } elseif ($quotation["discount_type"] == "2") {
        // Fixed Amount Discount
        if($data['total']['single'] > 0)
          $data['total']['single_discounted'] = $data['total']['single'] - ($quotation["discount"]);
        if($data['total']['double'] > 0)
          $data['total']['double_discounted'] = $data['total']['double'] - ($quotation["discount"]);
        if($data['total']['triple'] > 0)
          $data['total']['triple_discounted'] = $data['total']['triple'] - ($quotation["discount"]);
        if($data['total']['quadruple'] > 0)
          $data['total']['quadruple_discounted'] = $data['total']['quadruple'] -  ($quotation["discount"]);

        $data['total']['discounted_total'] = $data['total']['single_discounted'] * ($quotation['days'][1]['hotels']['single']??0 * 1) +
                                    $data['total']['double_discounted'] * ($quotation['days'][1]['hotels']['double']??0 * 2) +
                                    $data['total']['triple_discounted'] * ($quotation['days'][1]['hotels']['triple']??0 * 3) +
                                    $data['total']['quadruple_discounted'] * ($quotation['days'][1]['hotels']['quadruple']??0 * 4) +
                                    $data['total']['cwb'] +
                                    $data['total']['cnb'];

        $data['total']['discount'] = $data['total']['total'] - $data['total']['discounted_total'];
      } elseif ($quotation["discount_type"] == "3") {
        // Fixed Amount Discount
        //$data['total']['discount'] = $quotation["discount"];
        //$data['total']['discounted_total'] = $data['total']['total'] - ($quotation["discount"] / $usdToEurRate);
      }
    }

    session()->put('data.quotation.total', [
      "start" => $data['total']['total'],
      "discount" => $data['total']['discount'],
      "discounted_total" => $data['total']['discounted_total'],
    ]);
    if ($usdValue) {
      return $data;
    }

    if (isset($quotation['currency']) && $quotation['currency'] == 'EURO') {
      $isFixedEuro = (isset($quotation["discount_type"]) && $quotation["discount_type"] == "3") ? true : false;
      $usdToEurRate = Setting::getValue($quotation['currency'], 1.0);
      $data = self::convertToSelectedCurrency($data, $usdToEurRate, $isFixedEuro);
      $data['currency_symbol'] = "€";
    }

    return $data;
  }


  public static function checkIfQuotationChanged($oldQuotation, $newQuotation)
  {
    $keysToRemove = ["days", 'adult_markup', 'child_markup', 'markup_type', 'save_type', 'user', 'created_at', 'quotation_no', 'id'];
    $oldQuotation = QuotationService::removeElementsByName($oldQuotation, $keysToRemove);

    $differences = QuotationService::arrayRecursiveDiffAssoc($oldQuotation, $newQuotation);

    return [
      'has_changes' => !empty($differences),
      'differences' => $differences
    ];
  }

  static function removeElementsByName($originalArray, $keysToRemove)
  {
    // Flip the keys to remove and use array_diff_key to remove them from the original array
    $filteredArray = array_diff_key($originalArray, array_flip($keysToRemove));

    return $filteredArray;
  }

  static function arrayRecursiveDiffAssoc($array1, $array2)
  {
    $difference = [];

    if (!empty($array1) && !empty($array2)) {
      foreach ($array1 as $key => $value) {
        if (is_array($value)) {

          if (!isset($array2[$key]) || !is_array($array2[$key])) {
            $difference[$key] = $value;
          } else {
            $new_diff = QuotationService::arrayRecursiveDiffAssoc($value, $array2[$key]);
            if (!empty($new_diff)) {
              $difference[$key] = $new_diff;
            }
          }
        } elseif (!array_key_exists($key, $array2) || $array2[$key] !== $value) {
          $difference[$key] = $value;
        } else {
          // $difference[$key] = $value;
        }
      }
    } else {
      return $array2;
    }

    return $difference;
  }

  static function addMarkUp($quotation, $data)
  {
    dd($data);
    if (isset($quotation['markup_type']) && $quotation['markup_type'] == '1') {
      return QuotationService::addMarkupToArray($data, $quotation['adult_markup'], $quotation['child_markup']);
    }
  }

  static function addMarkupToArray($data, $adultMarkupPercentage, $childMarkupPercentage)
  {
    // Calculate the markup multiplier
    $adultMarkupMultiplier = 1 + ($adultMarkupPercentage / 100);
    $childMarkupMultiplier = 1 + ($childMarkupPercentage / 100);

    // Iterate over the array to apply the markup to all numeric values
    foreach ($data as $key => $value) {
      // If the value is an array, recursively call the function
      if (is_array($value)) {
        $data[$key] = QuotationService::addMarkupToArray($value, $adultMarkupPercentage, $childMarkupPercentage);
      }
      // If the value is numeric, apply the markup
      elseif (is_numeric($value)) {
        if ($key == "cwb" || $key == "cnb") {
          $data[$key] = number_format((float)($value * $childMarkupMultiplier), 2, '.', '');
        } else {
          $data[$key] = number_format((float)($value * $adultMarkupMultiplier), 2, '.', '');
        }
      }
    }

    return $data;
  }

  public function recreateQuotationWithNewDates($newQuotation, $quotationData, $dates)
  {
    $hotelService = new HotelService();
    $attractionController = new AttractionController();

    // Prepare the modified quotation array

    // Loop through the days to check if dates have changed
    foreach ($dates as $dayIndex => $dayData) {

      // $oldDate = $quotationData['days'][$dayIndex]['date']; // The old date from the existing quotation
      $newDate = $dayData['date']; // The new date from the passed data
      $hotels = $quotationData['days'][$dayIndex]['hotels'] ?? null;

      // Check if the date has changed
      if (isset($hotels)) {
        // Fetch new hotel rates based on the new date

        $roomStructure = $hotelService->getRoomTypeStructure($newQuotation, $dayIndex, true);

        $roomRate = $hotelService->getHotelRates($hotels['hotel-id'], $newDate, $hotels['meal'], array_keys(array_filter($roomStructure)), $hotels['room_category'], $quotationData['market']);

        // Update hotel rates in the quotation
        $quotationData['days'][$dayIndex]['hotels']['adult_rate[1]'] = 0;
        $quotationData['days'][$dayIndex]['hotels']['adult_rate[2]'] = 0;
        $quotationData['days'][$dayIndex]['hotels']['adult_rate[3]'] = 0;
        $quotationData['days'][$dayIndex]['hotels']['adult_rate[4]'] = 0;
        foreach ($roomRate as $rate) {
          $quotationData['days'][$dayIndex]['hotels']['adult_rate[' . $rate['room_type'] . ']'] = $rate['adult_rate'] ?? 0;
        }

        $quotationData['days'][$dayIndex]['hotels']['single'] = $roomStructure[1] ?? 0;
        $quotationData['days'][$dayIndex]['hotels']['double'] = $roomStructure[2] ?? 0;
        $quotationData['days'][$dayIndex]['hotels']['triple'] = $roomStructure[3] ?? 0;
        $quotationData['days'][$dayIndex]['hotels']['quadruple'] = $roomStructure[4] ?? 0;

        $quotationData['days'][$dayIndex]['hotels']['cwb_rate'] = $roomRate[0]['cwb'] ?? 0;
        $quotationData['days'][$dayIndex]['hotels']['cnb_rate'] = $roomRate[0]['cnb'] ?? 0;

        $quotationData['days'][$dayIndex]['date'] = $newDate;
      }

      $attractions = $quotationData['days'][$dayIndex]['attractions'] ?? null;

      if (isset($attractions)) {
        foreach ($attractions as $attractionIndex => $attraction) {
          $attrRate = $attractionController->getAttractionRates($attraction['attraction_type'], $attraction['attraction-id'], $dayIndex, $newQuotation);
          $quotationData['days'][$dayIndex]['attractions'][$attractionIndex]['adult_rate'] = $attrRate[0] ?? 0;
          $quotationData['days'][$dayIndex]['attractions'][$attractionIndex]['child_rate'] = $attrRate[1] ?? 0;
        }
      }
    }

    return $quotationData;
  }

  public function recreateQuotationWithVehicle($quotationData, $vehicle)
  {
    // Loop through the days to check if dates have changed
    foreach ($quotationData['days'] as $dayIndex => $dayData) {
      $quotationData['days'][$dayIndex]['transports']['rate'] = $vehicle['vehicle_rates'][0]['rate'];
    }
    return $quotationData;
  }


  public static function convertToSelectedCurrency(array $data, $rate, $isFixedEuro)
  {
    // Function to recursively convert USD to EUR in the array
    array_walk_recursive($data, function (&$value, $key) use ($rate, $isFixedEuro) {
      if (is_numeric($value)) {
        //if($isFixedEuro && $key == "discount") {
        //  $value = round($value, 2); // Convert and round to 2 decimal places
        //} else {
          $value = round($value * $rate, 2); // Convert and round to 2 decimal places
        //}

      }
    });

    return $data;
  }
}
