<?php

namespace App\Services;

use App\Models\Attraction;
use App\Models\CityTour;
use App\Models\Excursion;


class OtherService
{
  function calculateOtherRates($quotation)
  {
    // Initialize the room rates and total to zero
    $totalRate = 0;
    $singleRate = 0;
    $doubleRate = 0;
    $tripleRate = 0;
    $quadrupleRate = 0;
    $cwbRate = 0;
    $cnbRate = 0;

    // Initialize markup multipliers
    $adultMarkupMultiplier = 1;
    $childMarkupMultiplier = 1;
    $singleMarkupMultiplier = 1;
    $doubleMarkupMultiplier = 1;
    $tripleMarkupMultiplier = 1;
    $quadrupleMarkupMultiplier = 1;

    if(isset($quotation['markup_type']) && $quotation['markup_type'] == '1') {
        $adultMarkupMultiplier = 1 + ($quotation['adult_markup'] / 100);
        $childMarkupMultiplier = 1 + ($quotation['child_markup'] / 100);

        // Set room-specific multipliers
        $singleMarkupMultiplier = isset($quotation['single_markup']) && $quotation['single_markup'] > 0
            ? 1 + ($quotation['single_markup'] / 100)
            : $adultMarkupMultiplier;

        $doubleMarkupMultiplier = isset($quotation['double_markup']) && $quotation['double_markup'] > 0
            ? 1 + ($quotation['double_markup'] / 100)
            : $adultMarkupMultiplier;

        $tripleMarkupMultiplier = isset($quotation['triple_markup']) && $quotation['triple_markup'] > 0
            ? 1 + ($quotation['triple_markup'] / 100)
            : $adultMarkupMultiplier;

        $quadrupleMarkupMultiplier = isset($quotation['quadruple_markup']) && $quotation['quadruple_markup'] > 0
            ? 1 + ($quotation['quadruple_markup'] / 100)
            : $adultMarkupMultiplier;
    }

    // Loop through the days in the quotation
    foreach ($quotation['days'] as $key => $dayData) {
      if (isset($dayData['others']) && !empty($dayData['others'])) {
        foreach ($dayData['others'] as $otherData) {
          $dayRate = $otherData['other_rate'];
          $totalRate += add_markup($dayRate, $adultMarkupMultiplier);
        }
      }
    }

    $onePerson = $totalRate / $quotation['adults'];

    return [
      'single' => (isset($quotation['days'][1]['hotels']['single']) && $quotation['days'][1]['hotels']['single'] > 0) ? add_markup($onePerson, $singleMarkupMultiplier) : 0,
      'double' => (isset($quotation['days'][1]['hotels']['double']) && $quotation['days'][1]['hotels']['double'] > 0) ? add_markup($onePerson, $doubleMarkupMultiplier) : 0,
      'triple' => (isset($quotation['days'][1]['hotels']['triple']) && $quotation['days'][1]['hotels']['triple'] > 0) ? add_markup($onePerson, $tripleMarkupMultiplier) : 0,
      'quadruple' => (isset($quotation['days'][1]['hotels']['quadruple']) && $quotation['days'][1]['hotels']['quadruple'] > 0) ? add_markup($onePerson, $quadrupleMarkupMultiplier) : 0,
      'cwb' => 0,
      'cnb' => 0,
      'total' => $totalRate
    ];
  }

  function getOtherPNLData($quotation)
  {

    $groupedOtherData = []; // Will hold the attraction details
    $total = 0;

    foreach ($quotation['days'] as $key => $dayData) {
      if (isset($dayData['others']) && !empty($dayData['others'])) {
        foreach ($dayData['others'] as $otherData) {
            $dayRate = $otherData['other_rate'];

            $groupedOtherData['data'][] = [
                'other_title' => $otherData['other_title'],
                'date' => date('M d, Y', strtotime($dayData['date'])),
                'adult_price' => $otherData['other_rate'] / $quotation['adults'],
                'adult_count' => $quotation['adults'],
                'total' => $otherData['other_rate'],
            ];

            $total += $otherData['other_rate'];
        }
      }
    }

    $groupedOtherData['total'] = $total;

    return $groupedOtherData;
  }

}
