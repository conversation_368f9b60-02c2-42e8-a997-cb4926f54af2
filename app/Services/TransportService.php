<?php

namespace App\Services;

use DateTime;
use DateInterval;
use DatePeriod;

class TransportService
{
  public function calculateTransportRates($quotation)
  {
    // Initialize the room rates and total to zero
    $totalRate = 0;
    $singleRate = 0;
    $doubleRate = 0;
    $tripleRate = 0;
    $quadrupleRate = 0;
    $cwbRate = 0;
    $cnbRate = 0;

    // Initialize markup multipliers
    $adultMarkupMultiplier = 1;
    $childMarkupMultiplier = 1;

    if(isset($quotation['markup_type']) && $quotation['markup_type'] == '1') {
        $adultMarkupMultiplier = 1 + ($quotation['adult_markup'] / 100);
        $childMarkupMultiplier = 1 + ($quotation['child_markup'] / 100);
    }

    // Loop through the days in the quotation
    foreach ($quotation['days'] as $key => $dayData) {
      if (isset($dayData['transports']) && !empty($dayData['transports'])) {
        $distance = $dayData['transports']['distance'] ?? 0;
        $driver = $dayData['members'][0]['member_rate'] ?? 0;
        $guide = $dayData['members'][1]['member_rate']?? 0;
        $rate = $dayData['transports']['rate'] ?? 0;

        $dayRate = ($distance * $rate) + $driver + $guide;

        // Apply different markup multipliers based on room types
        if (isset($quotation['days'][1]['hotels']['single']) && $quotation['days'][1]['hotels']['single'] > 0) {
          $singleRate += add_markup($dayRate / $quotation['adults'], $adultMarkupMultiplier);
          $totalRate += add_markup($dayRate * ($quotation['days'][1]['hotels']['single'] / $quotation['adults']), $adultMarkupMultiplier);
        }

        if (isset($quotation['days'][1]['hotels']['double']) && $quotation['days'][1]['hotels']['double'] > 0) {
          $doubleRate += add_markup($dayRate / $quotation['adults'], $adultMarkupMultiplier);
          $totalRate += add_markup($dayRate * (($quotation['days'][1]['hotels']['double'] * 2) / $quotation['adults']), $adultMarkupMultiplier);
        }

        if (isset($quotation['days'][1]['hotels']['triple']) && $quotation['days'][1]['hotels']['triple'] > 0) {
          $tripleRate += add_markup($dayRate / $quotation['adults'], $adultMarkupMultiplier);
          $totalRate += add_markup($dayRate * (($quotation['days'][1]['hotels']['triple'] * 3) / $quotation['adults']), $adultMarkupMultiplier);
        }

        if (isset($quotation['days'][1]['hotels']['quadruple']) && $quotation['days'][1]['hotels']['quadruple'] > 0) {
          $quadrupleRate += add_markup($dayRate / $quotation['adults'], $adultMarkupMultiplier);
          $totalRate += add_markup($dayRate * (($quotation['days'][1]['hotels']['quadruple'] * 4) / $quotation['adults']), $adultMarkupMultiplier);
        }

        // If no specific room types are set, apply the adult markup to the total
        if (!isset($quotation['days'][1]['hotels']['single']) &&
            !isset($quotation['days'][1]['hotels']['double']) &&
            !isset($quotation['days'][1]['hotels']['triple']) &&
            !isset($quotation['days'][1]['hotels']['quadruple'])) {
          $totalRate += add_markup($dayRate, $adultMarkupMultiplier);
        }
      }
    }

    return [
      'single' => $singleRate,
      'double' => $doubleRate,
      'triple' => $tripleRate,
      'quadruple' => $quadrupleRate,
      'cwb' => $cwbRate,
      'cnb' => $cnbRate,
      'total' => $totalRate
    ];
  }


  public function getTransportPNLData($quotation)
  {
    $transportData = [];
    $totalAmount = 0;
    $totalKM = 0;

    // Loop through each day and extract transport data
    foreach ($quotation['days'] as $day => $dayData) {
        $transport = $dayData['transports'];

        $amount = $transport['distance'] * $transport['rate'];

        $amount += isset($dayData['members'][0]) ? $dayData['members'][0]['member_rate'] : 0;
        $amount += isset($dayData['members'][1]) ? $dayData['members'][1]['member_rate'] : 0;

        $transportData['data'][] = [
            'day' => $dayData['date'],
            'distance' => $transport['distance'],
            'rate' => $transport['rate'],
            'driver' => $dayData['members'][0]['member_rate']??0,
            'guide' => $dayData['members'][1]['member_rate']??0,
            'amount' => number_format($amount, 2),
        ];

        // Sum the total amount
        $totalAmount += $amount;
        $totalKM += $transport['distance'];
    }
    $transportData['total'] = $totalAmount;
    $transportData['totalKM'] = $totalKM;

    return $transportData;
  }

}
