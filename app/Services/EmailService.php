<?php

namespace App\Services;

use Illuminate\Support\Facades\Mail;
use App\Mail\QuotationMail;
use App\Mail\HotelVoucherMail;
use TijsVerkoyen\CssToInlineStyles\CssToInlineStyles;
use Illuminate\Support\Facades\Auth;

class EmailService
{
  /**
   * Send quotation email.
   *
   * @param array $quotationData
   * @param string $recipient
   * @return void
   */
  public function sendQuotationEmail($quotation, $dates, $hotels, $rates, $recipient)
  {
    // $coreCss = file_get_contents(public_path('assets/vendor/css/core.css'));
    // $themeCss = file_get_contents(public_path('assets/vendor/css/theme-default.css'));
    // $quotationCss = file_get_contents(public_path('assets/css/quotation.css')); // Load the local CSS file

    // // Combine all CSS into one string
    // $combinedCss = $coreCss . "\n" . $themeCss . "\n" . $quotationCss;

    // // Render the Blade template first
    // $htmlContent = view('emails.quotation', ['quotation' => $quotation, 'dates' => $dates, 'hotels' => $hotels, 'rates' => $rates])->render();

    // // Convert CSS to inline styles
    // $inliner = new CssToInlineStyles();
    // $htmlWithInlineStyles = $inliner->convert($htmlContent, $combinedCss);
    // return $htmlWithInlineStyles;

    // Use Laravel queue to send emails asynchronously
    $user = Auth::user();
    $signature = $user && $user->profile ? $user->profile->signature : null;

    Mail::to($recipient)->queue(new QuotationMail($quotation, $dates, $hotels, $rates, $signature));
  }

  /**
   * Send quotation email.
   *
   * @param array $quotationData
   * @param string $recipient
   * @return void
   */
  public function sendHotelVoucherEmail($quotation, $hotel, $hotelId, $remarks)
  {
    try {
      $user = Auth::user();
      $signature = $user && $user->profile ? $user->profile->signature : null;

      if (empty($hotel['contacts'])) {
          return false; // No email contacts provided
      }

      // Use Laravel queue to send emails asynchronously
      Mail::to($hotel['contacts'])->queue(new HotelVoucherMail($quotation, $hotel, $hotelId, $signature, $remarks));
      return true;
    } catch (\Exception $e) {
        return false;
    }
  }
}
