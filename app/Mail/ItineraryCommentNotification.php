<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use App\Models\QuotationComment;
use App\Models\Quotation;
use App\Models\RunningJob;
use TijsVerkoyen\CssToInlineStyles\CssToInlineStyles;

class ItineraryCommentNotification extends Mailable
{
    use Queueable, SerializesModels;

    public $comment;
    public $quotation;
    public $runningJob;
    public $referenceId;
    public $quotationNo;
    public $senderName;

    /**
     * Create a new message instance.
     *
     * @param QuotationComment $comment
     * @param Quotation $quotation
     * @param string $senderName
     * @return void
     */
    public function __construct(QuotationComment $comment, $quotation, $senderName)
    {
        $this->comment = $comment;
        $this->quotation = $quotation;
        $this->senderName = $senderName;
        $this->quotationNo = $quotation['quotation_no'];

        // Get running job if available
        $this->runningJob = RunningJob::where('quotation_id', $quotation['id'])->first();

        // Get reference ID if available
        $this->referenceId = $this->runningJob && $this->runningJob->booking ?
                             $this->runningJob->booking->reference_id : null;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        // Determine subject based on comment user's role
        $commentUser = $this->comment->user;
        $userRole = 'Client'; // Default role

        if ($commentUser && $commentUser->hasRole('Customer')) {
            $userRole = 'Client';
        } elseif ($commentUser && $commentUser->hasAnyRole(['Agent', 'Travel Consultant'])) {
            $userRole = 'Travel Consultant';
        }

        $subject = "Sri Lanka Viajes Eden - You Got a New Comment from a {$userRole}!";

        // Load the external CSS from the public directory
        $coreCss = file_get_contents(public_path('assets/vendor/css/core.css'));
        $themeCss = file_get_contents(public_path('assets/vendor/css/theme-default.css'));
        $quotationCss = file_get_contents(public_path('assets/css/quotation.css')); // Load the local CSS file

        // Extract CSS variables
        preg_match_all('/--([\w-]+):\s*(.*?);/', $coreCss, $matches);

        $variables = array_combine($matches[1], $matches[2]); // Convert to key-value array

        // Replace `var(--white)` with `#ffffff`, etc.
        foreach ($variables as $var => $value) {
            $coreCss = str_replace("var(--$var)", trim($value), $coreCss);
        }

        // Combine all CSS into one string
        $combinedCss = $coreCss . "\n" . $themeCss . "\n" . $quotationCss;

        // Render the Blade template first
        $htmlContent = view('emails.itinerary_comment_notification', [
            'comment' => $this->comment,
            'quotation' => $this->quotation,
            'runningJob' => $this->runningJob,
            'referenceId' => $this->referenceId,
            'quotationNo' => $this->quotationNo,
            'senderName' => $this->senderName
        ])->render();

        // Convert CSS to inline styles
        $inliner = new CssToInlineStyles();
        $htmlWithInlineStyles = $inliner->convert($htmlContent, $combinedCss);

        // Build the email with inline styles
        return $this->subject($subject)
                    ->html($htmlWithInlineStyles);
    }
}
