<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use App\Models\Conversation;
use App\Models\RunningJob;
use TijsVerkoyen\CssToInlineStyles\CssToInlineStyles;

class ConversationNotification extends Mailable
{
    use Queueable, SerializesModels;

    public $conversation;
    public $runningJob;
    public $bookingId;
    public $referenceId;
    public $quotationId;
    public $senderName;

    /**
     * Create a new message instance.
     *
     * @param Conversation $conversation
     * @param RunningJob $runningJob
     * @param string $senderName
     * @return void
     */
    public function __construct(Conversation $conversation, RunningJob $runningJob, $senderName)
    {
        $this->conversation = $conversation;
        $this->runningJob = $runningJob;
        $this->senderName = $senderName;

        // Get booking ID if available
        $this->bookingId = $runningJob->booking ? $runningJob->booking->id : null;

        // Get reference ID if available
        $this->referenceId = $runningJob->booking ? $runningJob->booking->reference_id : ($runningJob->quotation ? $runningJob->quotation->reference_id : null);

        // Get quotation numbers if available
        $this->quotationId = $runningJob->quotation_id ? $runningJob->quotation_id : [];
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $subject = "Sri Lanka Viajes Eden - You Got a New Comment from the Office! : {$this->referenceId}";

        // Load the external CSS from the public directory
        $coreCss = file_get_contents(public_path('assets/vendor/css/core.css'));
        $themeCss = file_get_contents(public_path('assets/vendor/css/theme-default.css'));
        $quotationCss = file_get_contents(public_path('assets/css/quotation.css')); // Load the local CSS file

        // Extract CSS variables
        preg_match_all('/--([\w-]+):\s*(.*?);/', $coreCss, $matches);

        $variables = array_combine($matches[1], $matches[2]); // Convert to key-value array

        // Replace `var(--white)` with `#ffffff`, etc.
        foreach ($variables as $var => $value) {
            $coreCss = str_replace("var(--$var)", trim($value), $coreCss);
        }

        // Combine all CSS into one string
        $combinedCss = $coreCss . "\n" . $themeCss . "\n" . $quotationCss;

        // Render the Blade template first
        $htmlContent = view('emails.conversation_notification', [
            'conversation' => $this->conversation,
            'runningJob' => $this->runningJob,
            'bookingId' => $this->bookingId,
            'referenceId' => $this->referenceId,
            'quotationId' => $this->quotationId,
            'senderName' => $this->senderName
        ])->render();

        // Convert CSS to inline styles
        $inliner = new \TijsVerkoyen\CssToInlineStyles\CssToInlineStyles();
        $htmlWithInlineStyles = $inliner->convert($htmlContent, $combinedCss);

        // Build the email with inline styles
        return $this->subject($subject)
                    ->html($htmlWithInlineStyles);
    }
}
