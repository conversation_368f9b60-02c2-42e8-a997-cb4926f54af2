<?php

namespace App\Helpers;

use Carbon\Carbon;

class DateHelper
{
    /**
     * Convert a date to localized format based on language
     *
     * @param string $date The date to convert
     * @param string $language The language code (en, es, fr, it)
     * @param bool $short Whether to use short format (default: true)
     * @return string The formatted date
     */
    public static function convertDateToLocale($date, $language = 'en', $short = true)
    {
        if (empty($date) || $date === 'N/A' || $date === null) {
            return '';
        }

        try {
            $carbonDate = Carbon::parse($date);
            $carbonDate->locale($language);
        } catch (\Exception $e) {
            return '';
        }

        // Format based on language and short/long preference
        if ($short) {
            // Short format for all languages
            return $carbonDate->isoFormat('D MMM YYYY');
        } else {
            // Long format based on language
            switch ($language) {
                case 'es':
                    return $carbonDate->isoFormat('D [de] MMMM [de] YYYY'); // Spanish: 1 de agosto de 2025
                case 'fr':
                    return $carbonDate->isoFormat('D MMMM YYYY'); // French: 1 août 2025
                case 'it':
                    return $carbonDate->isoFormat('D MMMM YYYY'); // Italian: 1 agosto 2025
                default:
                    return $carbonDate->isoFormat('MMMM D, YYYY'); // English: August 1, 2025
            }
        }
    }
}
