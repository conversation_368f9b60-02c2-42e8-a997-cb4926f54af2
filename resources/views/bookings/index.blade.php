@extends('layouts/contentNavbarLayout')

@section('title', 'Inquiries Management')

@section('vendor-style')
    <link rel="stylesheet" href="https://cdn.datatables.net/2.1.5/css/dataTables.bootstrap5.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/searchpanes/2.3.2/css/searchPanes.bootstrap5.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/select/2.0.5/css/select.bootstrap5.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/2.1.5/css/dataTables.dataTables.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/select/2.0.5/css/select.dataTables.css">
@endsection

@section('vendor-script')
    <script src="https://code.jquery.com/jquery-3.7.1.js"></script>
    <script src="https://cdn.datatables.net/2.1.5/js/dataTables.js"></script>
    <script src="https://cdn.datatables.net/2.1.5/js/dataTables.bootstrap5.js"></script>
    <script src="https://cdn.datatables.net/buttons/3.1.2/js/dataTables.buttons.js"></script>
    <script src="https://cdn.datatables.net/buttons/3.1.2/js/buttons.bootstrap5.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/3.1.2/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/3.1.2/js/buttons.print.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/3.1.2/js/buttons.colVis.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
@endsection

@section('page-style')
    <!-- Styles -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="{{ asset('assets/css/data-table.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/admin.css') }}">
@endsection

@section('content')
    <div class="mb-6 row g-6">
        <div class="col-sm-6 col-xl-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-start justify-content-between">
                        <div class="content-left">
                            <span class="text-heading">Total Inquiries</span>
                            <div class="my-1 d-flex align-items-center">
                                <h4 class="mb-0 me-2">{{ $totalBookings ?? 0 }}</h4>
                                <p class="mb-0 text-success">(+5%)</p>
                            </div>
                            <small class="mb-0">Total Inquiries in the system</small>
                        </div>
                        <div class="avatar">
                            <span class="rounded avatar-initial bg-label-primary">
                                <i class="bx bx-calendar-check bx-lg"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bookings List Table -->
    <div class="card">
        <div class="card-header border-bottom">
            <h5 class="my-3 mb-0 card-title d-inline-block">All Inquiries</h5>
            @if (hasAnyRole(['Super Admin', 'Admin', 'Travel Consultant', 'Accountant', 'Foreign Agent']))
                <button class="btn btn-primary float-end" id="addNewBooking" type="button" data-bs-toggle="offcanvas"
                    data-bs-target="#offcanvasAddBooking">
                    <i class="bx bx-plus me-1"></i> Add New Inquiry
                </button>
            @endif

            <!-- Filters Section -->
            <div class="mb-3 row mt-4">
                <div class="mb-3 col-3">
                    <label for="filterReferenceId">Filter by Reference ID:</label>
                    <input type="text" id="filterReferenceId" class="form-control" placeholder="Enter Reference ID">
                </div>
                <div class="mb-3 col-3">
                    <label for="filterClientHandler">Filter by Client Handler:</label>
                    <select id="filterClientHandler" class="form-select">
                        <option value="">All Handlers</option>
                        @foreach ($usersHandler as $handler)
                            <option value="{{ $handler->id }}">{{ $handler->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="mb-3 col-3">
                    <label for="filterAssignee">Filter by Quotation Assignee:</label>
                    <select id="filterAssignee" class="form-select" multiple>
                        @foreach ($usersAssign as $assignee)
                            <option value="{{ $assignee->id }}">{{ $assignee->name }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="mb-3 col-3">
                    <label for="filterStatus01">Filter by Status 01:</label>
                    <select id="filterStatus01" class="form-select" multiple>
                        <option value="Inquiry Received">Inquiry Received</option>
                        <option value="Assigned">Assigned</option>
                        <option value="Quotation / Itinerary Ready">Quotation / Itinerary Ready</option>
                        <option value="Sent to Client">Sent to Client</option>
                        <option value="Modification">Modification</option>
                        <option value="Modification completed">Modification completed</option>
                        <option value="Pending Payment">Pending Payment</option>
                        <option value="Pending Vouchers to be sent">Pending Vouchers to be sent</option>
                        <option value="Vouchers Sent">Vouchers Sent</option>
                        <option value="Hotel Confirmations pending">Hotel Confirmations pending</option>
                        <option value="Hotel Confirmations done">Hotel Confirmations done</option>
                        <option value="Chauffer Assigned">Chauffer Assigned</option>
                        <option value="Tour Completed">Tour Completed</option>
                    </select>
                </div>
            </div>
            <div class="mb-3 row">
                <div class="mb-3 col-3">
                    <label for="filterStatus02">Filter by Status 02:</label>
                    <select id="filterStatus02" class="form-select" multiple>
                        <option value="Tour to be Confirmed">Tour to be Confirmed</option>
                        <option value="Tour Confirmed">Tour Confirmed</option>
                        <option value="Tour Rejected">Tour Rejected</option>
                    </select>
                </div>
            </div>

            <div class="card-datatable table-responsive">
                <table class="table datatables-bookings border-top">
                    <thead>
                        <tr>
                            <th>Inquiry ID</th>
                            <th>Quotation No</th>
                            <th>Reference ID</th>
                            <th>Status 01</th>
                            <th>Status 02</th>
                            <th>Client Handler</th>
                            <th>Quotation Assignees</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                </table>
            </div>

            <!-- Offcanvas to add new booking -->
            <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasAddBooking"
                aria-labelledby="offcanvasAddBookingLabel">
                <div class="offcanvas-header border-bottom">
                    <h5 id="offcanvasAddBookingLabel" class="offcanvas-title">Add New Inquiry</h5>
                    <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas"
                        aria-label="Close"></button>
                </div>
                <div class="flex-grow-0 p-6 mx-0 offcanvas-body h-100">
                    <form id="addBookingForm" action="{{ route('bookings.store') }}" method="POST">
                        @csrf
                        <input type="hidden" name='created_from' value="office">
                        <div class="mb-3">
                            <label for="template_id" class="form-label">Tour Template</label>
                            <select id="template_id" name="template_id"
                                class="form-select select2 @error('template_id') is-invalid @enderror">
                                <option value="">Select a template</option>
                                @foreach ($templates as $template)
                                    <option value="{{ $template->id }}"
                                        {{ old('template_id') == $template->id ? 'selected' : '' }}>
                                        {{ $template->template_name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('template_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="reference_id" class="form-label">Reference ID</label>
                            <input type="text" class="form-control @error('reference_id') is-invalid @enderror"
                                id="reference_id" name="reference_id" value="{{ old('reference_id') }}"
                                placeholder="Enter Reference ID">
                            @error('reference_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="full_name" class="form-label">Full Name</label>
                            <input type="text" class="form-control @error('full_name') is-invalid @enderror"
                                id="full_name" name="full_name" value="{{ old('full_name') }}" required>
                            @error('full_name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control @error('email') is-invalid @enderror"
                                id="email" name="email" value="{{ old('email') }}" required>
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="text" class="form-control @error('phone') is-invalid @enderror"
                                id="phone" name="phone" value="{{ old('phone') }}" required>
                            @error('phone')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="pax" class="form-label">Number of Participants</label>
                            <input type="number" class="form-control @error('pax') is-invalid @enderror" id="pax"
                                name="pax" value="{{ old('pax') }}" min="1" required>
                            @error('pax')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="message" class="form-label">Message</label>
                            <textarea class="form-control @error('message') is-invalid @enderror" id="message" name="message" rows="4">{{ old('message') }}</textarea>
                            @error('message')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="whatsapp_contact"
                                name="whatsapp_contact" {{ old('whatsapp_contact') ? 'checked' : '' }}>
                            <label class="form-check-label" for="whatsapp_contact">
                                Contact via WhatsApp
                            </label>
                        </div>

                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary me-2">Create Inquiry</button>
                            <button type="button" class="btn btn-outline-secondary"
                                data-bs-dismiss="offcanvas">Cancel</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Offcanvas to edit booking -->
            <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasEditBooking"
                aria-labelledby="offcanvasEditBookingLabel">
                <div class="offcanvas-header border-bottom">
                    <h5 id="offcanvasEditBookingLabel" class="offcanvas-title">Edit Inquiry</h5>
                    <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas"
                        aria-label="Close"></button>
                </div>
                <div class="flex-grow-0 p-6 mx-0 offcanvas-body h-100">
                    <!-- Edit booking form here -->
                </div>
            </div>
        </div>
    @endsection

    @section('page-script')
        <script>
            window.usersHandler = @json($usersHandler);
            window.usersAssign = @json($usersAssign);
        </script>
        <script>
            $(document).ready(function() {
                // Initialize Select2 for template dropdown
                $('#template_id').select2({
                    placeholder: 'Search and select a template',
                    allowClear: true,
                    width: '100%',
                    dropdownParent: $('#offcanvasAddBooking')
                });

                // Initialize Select2 for multi-select filters
                $('#filterAssignee').select2({
                    placeholder: 'Select assignees',
                    allowClear: true,
                    width: '100%'
                });

                $('#filterStatus01').select2({
                    placeholder: 'Select status',
                    allowClear: true,
                    width: '100%'
                });

                $('#filterStatus02').select2({
                    placeholder: 'Select status',
                    allowClear: true,
                    width: '100%'
                });

                // Auto-populate tour name when template is selected
                $('#template_id').on('change', function() {
                    const templateId = $(this).val();
                    if (templateId) {
                        const selectedTemplate = $('#template_id option:selected').text().trim();
                        $('#name').val(selectedTemplate);
                    }
                });

                var table = $('.datatables-bookings').DataTable({
                    processing: true,
                    serverSide: true,
                    ajax: {
                        url: "{{ route('bookings.list') }}",
                        data: function(d) {
                            d.reference_id = $('#filterReferenceId').val();
                            d.assignee_id = $('#filterClientHandler').val();
                            d.quotation_assignee = $('#filterAssignee').val();
                            d.status_01 = $('#filterStatus01').val();
                            d.status_02 = $('#filterStatus02').val();
                        }
                    },
                    columns: [{
                            data: 'id',
                            name: 'id',
                            title: 'Inquiry ID'
                        },
                        {
                            data: 'quotation_no',
                            name: 'quotation_no',
                            title: 'Quotation No',
                            render: function(data, type, row) {
                                if (data !== 'N/A') {
                                    return `<span class="quotation-number" data-id="${row.running_job_id}">${data}</span>`;
                                } else {
                                    return `<input type="text" class="form-control update-quotation" data-id="${row.running_job_id}" placeholder="Enter Quotation #">`;
                                }
                            }
                        },
                        {
                            data: 'reference_id',
                            name: 'reference_id',
                            title: 'Reference ID',
                            render: function(data, type, row) {
                                if (data !== 'N/A') {
                                    return `<span class="reference-number" data-id="${row.running_job_id}">${data}</span>`;
                                } else {
                                    return `<input type="text" class="form-control update-reference" data-id="${row.running_job_id}" placeholder="Enter Reference #">`;
                                }
                            }
                        },
                        {
                            data: 'status_01',
                            name: 'status_01',
                            title: 'Status 01',
                            render: function(data, type, row) {
                                if (type === 'display') {
                                    const status01Options = [
                                        'Inquiry Received',
                                        'Assigned',
                                        'Quotation / Itinerary Ready',
                                        'Sent to Client',
                                        'Modification',
                                        'Modification completed',
                                        'Pending Payment',
                                        'Pending Vouchers to be sent',
                                        'Vouchers Sent',
                                        'Hotel Confirmations pending',
                                        'Hotel Confirmations done',
                                        'Chauffer Assigned',
                                        'Tour Completed',
                                    ];

                                    let select =
                                        `<select class="form-select status-01" data-id="${row.id}">`;
                                    select += `<option value="" >SELECT</option>`;
                                    status01Options.forEach(option => {
                                        select +=
                                            `<option value="${option}" ${data === option ? 'selected' : ''}>${option}</option>`;
                                    });
                                    select += '</select>';
                                    return select;
                                }
                                return data;
                            }
                        },
                        {
                            data: 'status_02',
                            name: 'status_02',
                            title: 'Status 02',
                            render: function(data, type, row) {
                                if (type === 'display') {
                                    const status02Options = ['Tour to be Confirmed', 'Tour Confirmed',
                                        'Tour Rejected'
                                    ];

                                    let html =
                                        `<div><select class="form-select status-02" data-id="${row.id}">`;
                                    html += `<option value="" >SELECT</option>`;
                                    status02Options.forEach(option => {
                                        html +=
                                            `<option value="${option}" ${data === option ? 'selected' : ''}>${option}</option>`;
                                    });
                                    html += '</select>';

                                    // If status is "Tour Rejected", show reason dropdown and comment field
                                    if (data === 'Tour Rejected') {
                                        html += `
                                        <select class="mt-2 form-select status-02-reason" data-id="${row.id}">
                                            <option value="">Select</option>
                                            <option ${row.status_02_reason === 'Tour Overpriced' ? 'selected' : ''}>Tour Overpriced</option>
                                            <option ${row.status_02_reason === 'Choose another destination' ? 'selected' : ''}>Choose another destination</option>
                                            <option ${row.status_02_reason === 'Choose another company' ? 'selected' : ''}>Choose another company</option>
                                            <option ${row.status_02_reason === 'Personal issue' ? 'selected' : ''}>Personal issue</option>
                                            <option ${row.status_02_reason === 'Choose to travel another year/future' ? 'selected' : ''}>Choose to travel another year/future</option>
                                        </select>
                                        <textarea class="mt-1 form-control status-02-comment" data-id="${row.id}" placeholder="Other reason...">${row.status_02_comment || ''}</textarea>
                                    `;
                                    }

                                    html += '</div>';
                                    return html;
                                }
                                return data;
                            }
                        },
                        {
                            data: 'assignee_id',
                            name: 'assignee_id',
                            title: 'Client Handler',
                            render: function(data, type, row) {
                                let userRoles = @json(auth()->user()->getRoleNames());
                                let authUserId = @json(auth()->user()->id);

                                // Check if current user is the assignee and has agent role
                                let isAssigneeAgent = (data == authUserId && userRoles.includes(
                                    'Agent'));

                                // Show select box if: Super Admin, Admin, or current user is assignee with agent role
                                if (userRoles.includes('Super Admin') || userRoles.includes('Admin') ||
                                    isAssigneeAgent) {
                                    let assigneeDropdown =
                                        `<select class="form-select booking-assignee" data-id="${row.id}">`;
                                    assigneeDropdown += `<option value="0" >SELECT</option>`;
                                    window.usersHandler.forEach(user => {
                                        assigneeDropdown += `<option value="${user.id}" ${
                                        data == user.id ? 'selected' : ''
                                    }>${user.name}</option>`;
                                    });
                                    assigneeDropdown += '</select>';
                                    return assigneeDropdown;
                                } else {
                                    // Return the name of the user based on the assignee_id
                                    let matchedUser = window.usersHandler.find(user => user.id == data);
                                    return matchedUser ? matchedUser.name : 'N/A';
                                }
                            }
                        },
                        {
                            data: 'quotation_assignees',
                            name: 'quotation_assignees',
                            title: 'Quotation Assignees',
                            orderable: false,
                            searchable: false,
                            render: function(data, type, row) {
                                let userRoles = @json(auth()->user()->getRoleNames());
                                let parsed = typeof data === 'string' ? JSON.parse(data) : data;
                                let selectedIds = Array.isArray(parsed) ? parsed.map(user => user.id) :
                                    [];
                                let authUserId = {{ auth()->user()->id }};

                                // Check if current user created this booking (through quotation)
                                let isBookingCreator = row.assignee_id == authUserId;

                                // Show select box if: Super Admin, Admin, user is assignee, or user created the booking
                                if (
                                    userRoles.some(role => ['Super Admin', 'Admin'].includes(role)) ||
                                    selectedIds.includes(authUserId) ||
                                    isBookingCreator
                                ) {
                                    let select =
                                        `<select class="form-select quotation-assignees" data-id="${row.running_job_id}" multiple style="width: 100%">`;
                                    window.usersAssign.forEach(user => {
                                        const selected = selectedIds.includes(user.id) ?
                                            'selected' : '';
                                        select +=
                                            `<option value="${user.id}" ${selected}>${user.name}</option>`;
                                    });
                                    select += `</select>`;
                                    return select;
                                } else {
                                    // Otherwise, show a comma-separated string of user names
                                    let names = window.usersAssign
                                        .filter(user => selectedIds.includes(user.id))
                                        .map(user => user.name)
                                        .join(', ');
                                    return names || 'N/A';
                                }
                            }
                        },
                        {
                            data: null,
                            name: 'actions',
                            title: 'Actions',
                            orderable: false,
                            searchable: false,
                            render: function(data, type, row) {
                                let userRoles = @json(auth()->user()->getRoleNames());
                                let authUserId = {{ auth()->user()->id }};
                                let parsed = typeof row.quotation_assignees === 'string' ? JSON.parse(
                                    row.quotation_assignees) : row.quotation_assignees;
                                let selectedIds = Array.isArray(parsed) ? parsed.map(user => user.id) :
                                    [];

                                // Check if user can view/manage this record
                                let canManage = (
                                    userRoles.some(role => ['Super Admin', 'Admin'].includes(
                                        role)) ||
                                    selectedIds.includes(authUserId) ||
                                    row.assignee_id == authUserId
                                );

                                // Check if user can delete (only Admins / Super Admins)
                                let canDelete = userRoles.some(role => ['Super Admin', 'Admin']
                                    .includes(role));

                                if (canManage) {
                                    let buttons = `
                                    <button type="button" class="btn btn-icon open-record text-warning me-2" data-id="${row.running_job_id}" data-name="${row.name}">
                                        <i class="bx bx-folder-open"></i>
                                    </button>
                                `;

                                    if (canDelete) {
                                        buttons += `
                                        <button type="button" class="btn btn-icon delete-record text-danger me-2" data-id="${row.id}" data-name="${row.name}">
                                            <i class="bx bx-trash"></i>
                                        </button>
                                    `;
                                    }

                                    return buttons;
                                } else {
                                    return "";
                                }
                            }
                        }
                    ],
                    pageLength: 10,
                    dom: "<'row'<'col-sm-12 col-md-6 my-3'B><'col-sm-12 col-md-6 my-3'f>>" +
                        "<'row'<'col-sm-12'tr>>" +
                        "<'row'<'col-sm-12 col-md-5'l><'col-sm-12 col-md-7'p>>",
                    lengthMenu: [
                        [10, 25, 50, 100],
                        [10, 25, 50, 100]
                    ],
                    language: {
                        paginate: {
                            previous: '<i class="bx bx-chevron-left bx-18px"></i>',
                            next: '<i class="bx bx-chevron-right bx-18px"></i>',
                        },
                        search: "Reference NO:",
                        lengthMenu: "Show _MENU_ entries",
                        info: "Displaying _START_ to _END_ of _TOTAL_ entries"
                    }
                });

                $('.datatables-bookings').on('draw.dt', function() {
                    $('.quotation-assignees').select2({
                        placeholder: "Select users...",
                        allowClear: true,
                        width: 'resolve'
                    });
                });

                // Handle status change
                $(document).on('change', '.booking-status', function() {
                    const bookingId = $(this).data('id');
                    const status = $(this).val();

                    $.ajax({
                        url: `/admin/bookings/${bookingId}/status`,
                        type: 'PATCH',
                        data: {
                            status: status,
                            _token: '{{ csrf_token() }}'
                        },
                        success: function(response) {
                            Swal.fire('Updated!', 'Booking status has been updated.', 'success');
                        },
                        error: function() {
                            Swal.fire('Error!', 'An error occurred while updating the status.',
                                'error');
                        }
                    });
                });

                // Handle assignee change
                $(document).on('change', '.booking-assignee', function() {
                    const bookingId = $(this).data('id');
                    const assigneeId = $(this).val();

                    $.ajax({
                        url: `/admin/bookings/${bookingId}/assignee`,
                        type: 'PATCH',
                        data: {
                            assignee_id: assigneeId,
                            _token: '{{ csrf_token() }}'
                        },
                        success: function(response) {
                            Swal.fire('Updated!', 'Booking assignee has been updated.', 'success');
                        },
                        error: function() {
                            Swal.fire('Error!', 'An error occurred while updating the assignee.',
                                'error');
                        }
                    });
                });

                // Handle delete button
                $(document).on('click', '.delete-record', function(e) {
                    e.preventDefault();
                    const bookingId = $(this).data('id');
                    const bookingName = $(this).data('name');

                    Swal.fire({
                        title: `Delete ${bookingName}?`,
                        text: 'This action cannot be undone.',
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonText: 'Yes, delete it!',
                    }).then(result => {
                        if (result.isConfirmed) {
                            $.ajax({
                                url: `/admin/bookings/${bookingId}`,
                                type: 'DELETE',
                                data: {
                                    _token: '{{ csrf_token() }}'
                                },
                                success: function(response) {
                                    Swal.fire('Deleted!',
                                            `${bookingName} has been deleted.`, 'success')
                                        .then(() => {
                                            table.ajax.reload(null, false);
                                        });
                                },
                                error: function() {
                                    Swal.fire('Error!',
                                        'An error occurred while deleting the booking.',
                                        'error');
                                }
                            });
                        }
                    });
                });

                // Status 01
                $(document).on('change', '.status-01', function() {
                    const bookingId = $(this).data('id');
                    const value = $(this).val();
                    $.ajax({
                        url: `/admin/bookings/${bookingId}/status-01`,
                        type: 'PATCH',
                        data: {
                            status_01: value,
                            _token: '{{ csrf_token() }}'
                        },
                        success: () => Swal.fire('Updated!', 'Status 01 updated.', 'success'),
                        error: () => Swal.fire('Error!', 'Failed to update Status 01.', 'error')
                    });
                });

                // Status 02
                $(document).on('change', '.status-02', function() {
                    const $row = $(this).closest('div');
                    const bookingId = $(this).data('id');
                    const status = $(this).val();

                    // If status is "Tour Rejected", show reason dropdown and comment field
                    if (status === 'Tour Rejected') {
                        if ($row.find('.status-02-reason').length === 0) {
                            $row.append(`
                            <select class="mt-2 form-select status-02-reason" data-id="${bookingId}">
                                <option value="">Select</option>
                                <option>Tour Overpriced</option>
                                <option>Choose another destination</option>
                                <option>Choose another company</option>
                                <option>Personal issue</option>
                                <option>Choose to travel another year/future</option>
                            </select>
                            <textarea class="mt-1 form-control status-02-comment" data-id="${bookingId}" placeholder="Other reason..."></textarea>
                        `);
                        }
                    } else {
                        // Remove reason dropdown and comment field if status is not "Tour Rejected"
                        $row.find('.status-02-reason, .status-02-comment').remove();
                    }

                    const comment = $row.find('.status-02-comment').val() || '';

                    $.ajax({
                        url: `/admin/bookings/${bookingId}/status-02`,
                        type: 'PATCH',
                        data: {
                            status_02: status,
                            status_02_comment: comment,
                            _token: '{{ csrf_token() }}'
                        },
                        success: () => Swal.fire('Updated!', 'Status 02 updated.', 'success'),
                        error: () => Swal.fire('Error!', 'Failed to update Status 02.', 'error')
                    });
                });

                // Status 02 reason/comment change handler
                $(document).on('change keyup', '.status-02-reason, .status-02-comment', function() {
                    const $row = $(this).closest('div');
                    const bookingId = $(this).data('id');
                    const status = $row.find('.status-02').val();
                    const comment = $row.find('.status-02-comment').val() || '';

                    $.ajax({
                        url: `/admin/bookings/${bookingId}/status-02`,
                        type: 'PATCH',
                        data: {
                            status_02: status,
                            status_02_comment: comment,
                            _token: '{{ csrf_token() }}'
                        },
                        success: () => Swal.fire('Updated!', 'Status 02 updated.', 'success'),
                        error: () => Swal.fire('Error!', 'Failed to update Status 02.', 'error')
                    });
                });

                // Status 03 - checkboxes
                $(document).on('change', '.status-03-checkbox', function() {
                    const $row = $(this).closest('tr');
                    const bookingId = $(this).data('id');
                    const values = [];

                    $row.find('.status-03-checkbox:checked').each(function() {
                        values.push($(this).val());
                    });

                    $.ajax({
                        url: `/admin/bookings/${bookingId}/status-03`,
                        type: 'PATCH',
                        data: {
                            status_03: values,
                            _token: '{{ csrf_token() }}'
                        },
                        success: () => Swal.fire('Updated!', 'Status 03 updated.', 'success'),
                        error: () => Swal.fire('Error!', 'Failed to update Status 03.', 'error')
                    });
                });

                // Add custom checkbox
                $(document).on('keypress', '.add-status-03', function(e) {
                    if (e.which === 13) {
                        const $this = $(this);
                        const bookingId = $this.data('id');
                        const newItem = $this.val().trim();
                        if (!newItem) return;

                        $.ajax({
                            url: `/admin/bookings/${bookingId}/status-03-custom`,
                            type: 'PATCH',
                            data: {
                                item: newItem,
                                _token: '{{ csrf_token() }}'
                            },
                            success: () => {
                                $this.before(`<div class="form-check">
                    <input class="form-check-input status-03-checkbox" type="checkbox" data-id="${bookingId}" value="${newItem}" checked>
                    <label class="form-check-label">${newItem}</label>
                </div>`);
                                $this.val('');
                            },
                            error: () => Swal.fire('Error!', 'Failed to add custom item.', 'error')
                        });
                    }
                });

                // Handle quotation assignee change
                $(document).on('change', '.quotation-assignees', function() {
                    const runningJobId = $(this).data('id');
                    const selectedUserIds = $(this).val(); // This will be an array of user IDs

                    $.ajax({
                        url: `/admin/running-jobs/${runningJobId}/quotation-assignees`,
                        type: 'PATCH',
                        data: {
                            user_ids: selectedUserIds,
                            _token: '{{ csrf_token() }}'
                        },
                        success: function(response) {
                            Swal.fire('Updated!', 'Quotation assignees have been updated.',
                                'success');
                        },
                        error: function() {
                            Swal.fire('Error!', 'Failed to update quotation assignees.', 'error');
                        }
                    });
                });

                $(document).on('click', '.open-record', function() {
                    var runningJobId = $(this).data('id');
                    window.location.href = `/admin/running-jobs/${runningJobId}/history`;
                });

                // Handle inline quotation update
                $(document).on('keypress', '.update-quotation', function(event) {
                    if (event.which === 13) { // Enter key pressed
                        let runningJobId = $(this).data('id');
                        let newQuotationNumber = $(this).val();

                        if (newQuotationNumber.trim() !== "") {
                            $.ajax({
                                url: `/admin/running-jobs/${runningJobId}/update-quotation`,
                                type: 'PATCH',
                                data: {
                                    quotation_number: newQuotationNumber,
                                    _token: '{{ csrf_token() }}'
                                },
                                success: function(response) {
                                    Swal.fire('Updated!', 'Quotation number has been updated.',
                                        'success');
                                    $('.datatables-running-jobs').DataTable().ajax.reload(null,
                                        false);
                                },
                                error: function() {
                                    Swal.fire('Error!', 'Failed to update quotation number.',
                                        'error');
                                }
                            });
                        }
                    }
                });

                // Handle inline reference ID update
                $(document).on('keypress', '.update-reference', function(event) {
                    if (event.which === 13) { // Enter key pressed
                        let runningJobId = $(this).data('id');
                        let newReferenceId = $(this).val();

                        if (newReferenceId.trim() !== "") {
                            $.ajax({
                                url: `/admin/bookings/${runningJobId}/update-reference-id`,
                                type: 'PATCH',
                                data: {
                                    running_job_id: runningJobId,
                                    reference_id: newReferenceId,
                                    _token: '{{ csrf_token() }}'
                                },
                                success: function(response) {
                                    if (response.success) {
                                        // Replace input with span showing the reference ID
                                        $(`.update-reference[data-id="${runningJobId}"]`)
                                            .replaceWith(
                                                `<span class="reference-number" data-id="${runningJobId}">${newReferenceId}</span>`
                                            );

                                        Swal.fire('Updated!', 'Reference ID has been updated.',
                                            'success');
                                    }
                                },
                                error: function() {
                                    Swal.fire('Error!', 'Failed to update Reference ID.', 'error');
                                }
                            });
                        }
                    }
                });

                // Add event listeners for the filters
                $('#filterQuotationNo, #filterReferenceId').on('keyup', function() {
                    table.ajax.reload();
                });

                $('#filterClientHandler, #filterAssignee, #filterStatus01, #filterStatus02').on('change', function() {
                    table.ajax.reload();
                });
            });
        </script>
    @endsection
