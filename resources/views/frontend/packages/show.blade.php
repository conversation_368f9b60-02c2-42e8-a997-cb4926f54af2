<?php $language = session('locale', app()->getLocale()); ?>
@extends('layouts.frontend.app')

@section('title', $templateDetails['template_name'] . ' - Viajes Eden')

@section('content')
    <section id="stats" class="stats section dark-background">

        <img src="{{ $templateDetails['image'] }}" alt="" data-aos="fade-in" class="aos-init aos-animate">

        <div class="container position-relative aos-init aos-animate" data-aos="fade-up" data-aos-delay="100">

            <div class="row gy-4">
                <h1 data-aos="fade-up" class="text-center aos-init aos-animate">{{ $templateDetails['template_name'] }}</h1>
            </div>

            <div class="row gy-4">

                <div class="col-lg-3 col-md-6">
                    <div class="text-center stats-item w-100 h-100">
                        <span data-purecounter-start="0" data-purecounter-end="232" data-purecounter-duration="0"
                            class="purecounter">{{ $templateDetails['pp_price'] }}</span>
                        <p>PP Price</p>
                    </div>
                </div><!-- End Stats Item -->

                <div class="col-lg-3 col-md-6">
                    <div class="text-center stats-item w-100 h-100">
                        <span data-purecounter-start="0" data-purecounter-end="521" data-purecounter-duration="0"
                            class="purecounter">{{ $templateDetails['days_count'] }}</span>
                        <p>Days</p>
                    </div>
                </div><!-- End Stats Item -->

                <div class="col-lg-3 col-md-6">
                    <div class="text-center stats-item w-100 h-100">
                        <span data-purecounter-start="0" data-purecounter-end="1453" data-purecounter-duration="0"
                            class="purecounter">BB</span>
                        <p>Meals</p>
                    </div>
                </div><!-- End Stats Item -->

                <div class="col-lg-3 col-md-6">
                    <div class="text-center stats-item w-100 h-100">
                        <span data-purecounter-start="0" data-purecounter-end="32" data-purecounter-duration="0"
                            class="purecounter">Any</span>
                        <p>Pax</p>
                    </div>
                </div><!-- End Stats Item -->

            </div>

        </div>

    </section>

    <section id="about" class="about section">

        <!-- Section Title -->
        <div class="container section-title aos-init aos-animate" data-aos="fade-up">
            <h2>About Package<br></h2>
            <p><span>Journey Schedule</span> <span class="description-title">Itinerary</span></p>
        </div><!-- End Section Title -->

        <div class="container">
            @foreach ($templateDetails['quotation']['days'] ?? [] as $day)
                @if (isset($day['day']))
                    <div class="row gy-4">

                        <div class="contact row">
                            <div class="col-md-6">
                                <div class="info-item d-flex align-items-center aos-init aos-animate" data-aos="fade-up"
                                    data-aos-delay="200">
                                    <i class="flex-shrink-0 icon bi bi-geo-alt"></i>
                                    <div>
                                        <h3>Day {{ $day['day'] }}</h3>
                                        <p>{{ App\Models\Place::find($day['end'])->name }}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="info-item d-flex align-items-center aos-init aos-animate" data-aos="fade-up"
                                    data-aos-delay="200">
                                    <i class="flex-shrink-0 icon bi bi-house-fill"></i>
                                    <div>
                                        <h3>Hotel</h3>
                                        <p>{{ \App\Models\Hotel::find($day['hotels']['hotel-id'])->name }}</p>
                                    </div>
                                </div>
                            </div>


                        </div>

                        <div class="row">
                            <div class="col-lg-4 col-sm-12 aos-init aos-animate" data-aos="fade-up" data-aos-delay="100">
                                @if (\App\Models\Place::find($day['start'])->image)
                                    @if (filter_var(\App\Models\Place::find($day['start'])->image, FILTER_VALIDATE_URL))
                                        <img src="{{ \App\Models\Place::find($day['start'])->image }}" alt="attrData Image"
                                            class="img-thumbnail" width="200">
                                    @else
                                        <img src="{{ (new App\Models\Place())->getImageUrl(\App\Models\Place::find($day['start'])->image) }}"
                                            class="mb-4 img-fluid" alt="">
                                    @endif
                                @endif
                            </div>
                            <div class="col-lg-8 col-sm-12 aos-init aos-animate" data-aos="fade-up" data-aos-delay="250">
                                <div class="content ps-0 ps-lg-5">
                                    <p class="fst-italic">
                                        {!! App\Models\Place::find($day['end'])->description !!}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            @foreach ($day['attractions'] ?? [] as $attraction)
                                @php
                                    if ($attraction['attraction_type'] == 'attraction') {
                                        $attrData = App\Models\Attraction::find($attraction['attraction-id']);
                                    } elseif ($attraction['attraction_type'] == 'city_tour') {
                                        $attrData = App\Models\CityTour::find($attraction['attraction-id']);
                                    } elseif ($attraction['attraction_type'] == 'excursion') {
                                        $attrData = App\Models\Excursion::find($attraction['attraction-id']);
                                    }
                                @endphp
                                <div class="flex-wrap gap-2 mb-2 justify-content-between">
                                    <div class="flex-wrap d-flex align-items-center mb-50 row">
                                        <div class="col-lg-3 col-sm-12">
                                            @if ($attrData->image)
                                                @if (filter_var($attrData->image, FILTER_VALIDATE_URL))
                                                    <img src="{{ $attrData->image }}" alt="attrData Image"
                                                        class="img-thumbnail" width="200">
                                                @else
                                                    <img src="{{ (new App\Models\Attraction())->getImageUrl($attrData->image) }}"
                                                        alt="Attraction Image" width="100%">
                                                @endif
                                            @endif
                                        </div>
                                        <div class="col-lg-9 col-sm-12">
                                            <strong>{{ $attrData->name }}</strong>
                                            <p class="mb-0">{!! $attrData->description !!}</p>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                    <hr class="seperator">
                @endif
            @endforeach
        </div>

    </section>

    <section id="book-a-table" class="book-a-table section">

        <!-- Section Title -->
        <div class="container section-title aos-init aos-animate" data-aos="fade-up">
            <h2>Stay With Us</h2>
            <p><span>Book This</span> <span class="description-title">Package<br></span></p>
        </div><!-- End Section Title -->

        <div class="container">

            <div class="row g-0 aos-init aos-animate" data-aos="fade-up" data-aos-delay="100">
                <div class="col-lg-12 d-flex align-items-center reservation-form-bg aos-init aos-animate" data-aos="fade-up"
                    data-aos-delay="200">
                    <form action="{{ route('bookings.store') }}" method="post" role="form" class="php-email-form">
                        @csrf
                        <input type="hidden" name="template_id" class="form-control" id="template_id"
                            placeholder="{{ __('form.placeholders.tour_name', [], $language) }}" required=""
                            value="{{ $templateDetails['template_id'] }}">

                        <div class="row gy-4">
                            <div class="col-lg-4 col-md-6">
                                <label for="form-field-name"
                                    class="form-label">{{ __('form.labels.tour_itinerary', [], $language) }}</label>
                                <input type="text" name="name" id="form-field-name" class="form-control"
                                    value="{{ $templateDetails['template_name'] }}" required>
                            </div>

                            <div class="col-lg-4 col-md-6">
                                <label for="form-field-field_db303c7"
                                    class="form-label">{{ __('form.labels.full_name', [], $language) }}</label>
                                <input type="text" name="full_name" id="form-field-field_db303c7"
                                    class="form-control" required>
                            </div>

                            <div class="col-lg-4 col-md-6">
                                <label for="form-field-field_e7234ec"
                                    class="form-label">{{ __('form.labels.no_of_participants', [], $language) }}</label>
                                <input type="number" name="pax" id="form-field-field_e7234ec" class="form-control"
                                    required>
                            </div>

                            <div class="col-lg-4 col-md-6">
                                <label for="form-field-field_7a3ed6c"
                                    class="form-label">{{ __('form.labels.mobile_no', [], $language) }}</label>
                                <input type="tel" name="phone" id="form-field-field_7a3ed6c" class="form-control"
                                    placeholder="{{ __('form.placeholders.mobile_example', [], $language) }}" required
                                    pattern="[0-9()#&amp;+*-=.]+"
                                    title="{{ __('form.placeholders.phone_characters', [], $language) }}">
                            </div>

                            <div class="col-lg-4 col-md-6">
                                <label for="form-field-email"
                                    class="form-label">{{ __('form.labels.email', [], $language) }}</label>
                                <input type="email" name="email" id="form-field-email" class="form-control"
                                    required>
                            </div>

                            <div class="col-lg-4 col-md-6">
                                <label for="form-field-email" class="form-label" style="width: 100%"></label>
                                <input class="form-check-input" type="checkbox" name="whatsapp_contact"
                                    id="form-field-field_b1520c7">
                                <label class="form-check-label" for="form-field-field_b1520c7">
                                    {{ __('form.labels.contact_on_whatsapp', [], $language) }}
                                </label>
                            </div>
                        </div>

                        <div class="mt-3 form-group">
                            <label for="form-field-message"
                                class="form-label">{{ __('form.labels.comments', [], $language) }}</label>
                            <textarea class="form-control" name="message" id="form-field-message" rows="4"></textarea>
                        </div>

                        <div class="mt-3 text-center">
                            <div class="loading">{{ __('form.status.loading', [], $language) }}</div>
                            <div class="error-message"></div>
                            <div class="sent-message">{{ __('form.status.sent_message', [], $language) }}</div>
                            <button type="submit">{{ __('form.buttons.submit', [], $language) }}</button>
                        </div>
                    </form>

                </div><!-- End Reservation Form -->

            </div>

        </div>

    </section>
@endsection


@section('scripts')
    <script>
        $(document).ready(function() {
            $(document).on('submit', '.php-email-form', function(e) {
                e.preventDefault();

                const form = $(this);
                const formData = form.serialize();
                // Show loading message
                form.find('.loading').show();
                form.find('.error-message').hide();
                form.find('.sent-message').hide();

                $.ajax({
                    url: form.attr('action'),
                    type: form.attr('method'),
                    data: formData,
                    success: function(response) {
                        // Hide loading and error, show success message
                        form.find('.loading').hide();
                        form.find('.error-message').hide();
                        form.find('.sent-message').show();

                        form[0].reset();
                    },
                    error: function() {
                        // Hide loading and success, show error message
                        form.find('.loading').hide();
                        form.find('.sent-message').hide();
                        form.find('.error-message').text('An error occurred: ' + (xhr
                                .responseJSON?.message || 'Unable to process your request'))
                            .show();
                    },
                });
            });
        });
    </script>
@endsection
