<div class="card app-chat">
    <div class="card-header">
        <!--h5 class="card-title">Conversation for Running Job</h5-->
    </div>
    <div class="card-body">
        <div class="mb-4">
            <small class="card-text text-uppercase text-muted small">Inquiry Details</small>
            <ul class="py-1 my-3 list-unstyled">
                <li class="mb-4 d-flex align-items-center">
                    <span class="col-4">
                        <i class="bx bx-user"></i>
                        <span class="mx-2 fw-medium">Inquiry ID:</span>
                    </span>
                    <span>{{ $runningJob->booking_id }}</span>
                </li>
                <hr>

                <li class="mb-4 d-flex align-items-center">
                    <span class="col-4">
                        <i class="bx bx-detail"></i>
                        <span class="mx-2 fw-medium">Inquiry Details:</span>
                    </span>
                    <span>{!! nl2br($runningJob->booking->message) !!}</span>
                </li>
                <hr>

                <li class="mb-4 d-flex align-items-center">
                    <span class="col-4">
                        <i class="bx bx-user-pin"></i>
                        <span class="mx-2 fw-medium">Client Handler:</span>
                    </span>

                    @php
                        $userRoles = auth()->user()->getRoleNames();
                        $authUserId = auth()->user()->id;
                    @endphp

                    @if (in_array('Super Admin', $userRoles->toArray()) || in_array('Admin', $userRoles->toArray()))
                        <div class="col-8">
                            <select class="form-select booking-assignee" data-id="{{ $runningJob->booking->id }}">
                                <option value="0">SELECT</option>
                                @foreach ($usersHandler ?? [] as $user)
                                    <option value="{{ $user->id }}"
                                        {{ isset($runningJob->booking->assignee_id) && $runningJob->booking->assignee_id == $user->id ? 'selected' : '' }}>
                                        {{ $user->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    @else
                        <span>{{ isset($runningJob->booking->assignee_id) ? \App\Models\User::findOrFail($runningJob->booking->assignee_id)->name ?? 'N/A' : 'N/A' }}</span>
                    @endif
                </li>
                <hr>

                <!-- Status 01 Field -->
                <li class="mb-4 d-flex align-items-center">
                    <span class="col-4">
                        <i class="bx bx-check-circle"></i>
                        <span class="mx-2 fw-medium">Status 01:</span>
                    </span>
                    <div class="col-8">
                        <select class="form-select status-01" data-id="{{ $runningJob->booking->id }}">
                            @php
                                $status01Options = [
                                    'Inquiry Received',
                                    'Assigned',
                                    'Quotation / Itinerary Ready',
                                    'Sent to Client',
                                    'Modification',
                                    'Modification completed',
                                    'Pending Payment',
                                    'Pending Vouchers to be sent',
                                    'Vouchers Sent',
                                    'Hotel Confirmations pending',
                                    'Hotel Confirmations done',
                                    'Chauffer Assigned',
                                    'Tour Completed',
                                ];
                            @endphp
                            @foreach ($status01Options as $option)
                                <option value="{{ $option }}"
                                    {{ $runningJob->booking->status_01 === $option ? 'selected' : '' }}>
                                    {{ $option }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </li>
                <hr>

                <!-- Status 02 Field -->
                <li class="mb-4 d-flex align-items-center">
                    <span class="col-4">
                        <i class="bx bx-check-square"></i>
                        <span class="mx-2 fw-medium">Status 02:</span>
                    </span>
                    <div class="col-8">
                        <select class="form-select status-02" data-id="{{ $runningJob->booking->id }}">
                            @php
                                $status02Options = ['Tour to be Confirmed', 'Tour Confirmed', 'Tour Rejected'];
                            @endphp
                            @foreach ($status02Options as $option)
                                <option value="{{ $option }}"
                                    {{ $runningJob->booking->status_02 === $option ? 'selected' : '' }}>
                                    {{ $option }}
                                </option>
                            @endforeach
                        </select>

                        @if ($runningJob->booking->status_02 === 'Tour Rejected')
                            <select class="mt-2 form-select status-02-reason" data-id="{{ $runningJob->booking->id }}">
                                <option
                                    {{ $runningJob->booking->status_02_reason === 'Tour Overpriced' ? 'selected' : '' }}>
                                    Tour Overpriced</option>
                                <option
                                    {{ $runningJob->booking->status_02_reason === 'Choose another destination' ? 'selected' : '' }}>
                                    Choose another destination</option>
                                <option
                                    {{ $runningJob->booking->status_02_reason === 'Choose another company' ? 'selected' : '' }}>
                                    Choose another company</option>
                                <option
                                    {{ $runningJob->booking->status_02_reason === 'Personal issue' ? 'selected' : '' }}>
                                    Personal issue</option>
                                <option
                                    {{ $runningJob->booking->status_02_reason === 'Choose to travel another year/future' ? 'selected' : '' }}>
                                    Choose to travel another year/future</option>
                            </select>
                            <textarea class="mt-1 form-control status-02-comment" data-id="{{ $runningJob->booking->id }}"
                                placeholder="Other reason...">{{ $runningJob->booking->status_02_comment }}</textarea>
                        @endif
                    </div>
                </li>
                <hr>

                <!-- Status 03 Field -->
                <li class="mb-4">
                    <div class="d-flex align-items-center">
                        <span class="col-4">
                            <i class="bx bx-list-check"></i>
                            <span class="mx-2 fw-medium">Status 03:</span>
                        </span>
                        <div class="col-8">
                            @php
                                $status03Items = [
                                    'Passports received',
                                    'Air Tickets received',
                                    'Hotel Reservation Vouchers sent',
                                    'Hotel Confirmations sent',
                                ];
                                $checkedItems = is_array($runningJob->booking->status_03)
                                    ? $runningJob->booking->status_03
                                    : (json_decode($runningJob->booking->status_03, true) ?:
                                    []);
                            @endphp

                            @foreach ($status03Items as $item)
                                <div class="form-check">
                                    <input class="form-check-input status-03-checkbox" type="checkbox"
                                        data-id="{{ $runningJob->booking->id }}" value="{{ $item }}"
                                        {{ in_array($item, $checkedItems) ? 'checked' : '' }}>
                                    <label class="form-check-label">{{ $item }}</label>
                                </div>
                            @endforeach

                            <!-- Custom items from database -->
                            @if (is_array($checkedItems))
                                @foreach ($checkedItems as $item)
                                    @if (!in_array($item, $status03Items))
                                        <div class="form-check">
                                            <input class="form-check-input status-03-checkbox" type="checkbox"
                                                data-id="{{ $runningJob->booking->id }}" value="{{ $item }}"
                                                checked>
                                            <label class="form-check-label">{{ $item }}</label>
                                        </div>
                                    @endif
                                @endforeach
                            @endif

                            <input type="text" class="mt-2 form-control add-status-03" placeholder="Add more..."
                                data-id="{{ $runningJob->booking->id }}">
                        </div>
                    </div>
                </li>
                <hr>

                <li class="mb-4 d-flex align-items-center">
                    <span class="col-4">
                        <i class="bx bx-user-pin"></i>
                        <span class="mx-2 fw-medium">Quotation Assignees:</span>
                    </span>
                    <div class="col-8">
                        @php
                            $selectedIds = $runningJob?->quotationAssignees->pluck('id')->toArray() ?? [];
                        @endphp

                        @if (in_array('Super Admin', $userRoles->toArray()) ||
                                in_array('Admin', $userRoles->toArray()) ||
                                in_array($authUserId, $selectedIds))
                            <select class="form-select quotation-assignees" data-id="{{ $runningJob->id }}" multiple
                                style="width: 100%">
                                @foreach ($usersAssign ?? [] as $user)
                                    <option value="{{ $user->id }}"
                                        {{ in_array($user->id, $selectedIds) ? 'selected' : '' }}>
                                        {{ $user->name }}
                                    </option>
                                @endforeach
                            </select>
                        @else
                            @foreach ($runningJob?->quotationAssignees as $user)
                                <span>{{ $user->name }}{{ !$loop->last ? ', ' : '' }}</span>
                            @endforeach
                        @endif
                    </div>
                </li>
                <hr>

                <li class="mb-4 d-flex align-items-center">
                    <span class="col-4">
                        <i class="bx bx-file"></i>
                        <span class="mx-2 fw-medium">Reference Number/ Quotation No:</span>
                    </span>
                    @if ($runningJob->quotation)
                        <span>{{ $runningJob->quotation->reference_no ?? 'NA' }} </span> /
                        <span id="quotation_no">{{ $runningJob->quotation->quotation_no ?? 'NA' }}</span>
                    @else
                        <span class="col-8">
                            <!--span class="col-4">
                                <input type="text" class="form-control update-quotation"
                                    data-id="{{ $runningJob->id }}" placeholder="Enter Quotation #">
                            </span-->
                            <div class="mb-4">
                                @if ($runningJob->booking->reference_id)
                                    <span class="reference-number"
                                        data-id="{{ $runningJob->id }}">{{ $runningJob->booking->reference_id }}</span>
                                @else
                                    <input type="text" class="form-control update-reference"
                                        data-id="{{ $runningJob->id }}"
                                        placeholder="Enter Reference ID and press Enter">
                                @endif
                            </div>
                            <div class="mb-4 ">
                                <a href="/admin/quotation/start?source_type=running_job&running_job_id={{ $runningJob->id }}"
                                    class="btn add-new btn-primary" id="addCustomQuotation">
                                    <span>
                                        <i class="bx bx-plus bx-sm me-0 me-sm-2"></i>
                                        <span class="d-none d-sm-inline-block">Create New Quotation</span></span></a>
                            </div>
                        </span>
                    @endif
                </li>
                <hr>

                <li class="mb-4 d-flex align-items-center">
                    <span class="col-4">
                        <i class="bx bx-file"></i>
                        <span class="mx-2 fw-medium">Air Ticket:</span>
                    </span>
                    <span class="col-8">
                        <input type="file" class="form-control update-air-ticket"
                            data-id="{{ $runningJob->booking->id }}" placeholder="Enter Air Ticket #">
                    </span>
                </li>
                <hr>

                <li class="mb-4 d-flex align-items-center">
                    <span class="col-4">
                        <i class="bx bx-file"></i>
                        <span class="mx-2 fw-medium">Passport:</span>
                    </span>
                    <span class="col-8">
                        <input type="file" class="form-control update-passport"
                            data-id="{{ $runningJob->booking->id }}" placeholder="Enter Passport #">
                    </span>
                </li>
                <hr>
            </ul>

            @if ($runningJob->quotation)
                <div class="card-datatable">
                    <table class="table datatables-quotations border-top">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Quotation No.</th>
                                <th>Reference No.</th>
                                <th>Date</th>
                                <th>Status</th>
                                <th>Author</th>
                                <th>Total Amount</th>
                                <th>Version</th>
                                <th>Enabled/Disabled</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            @endif

            <ul class="py-1 my-3 list-unstyled">
                @if (!empty($runningJob->images))
                    <li class="mb-4 d-flex align-items-center">
                        <i class="bx bx-image"></i>
                        <span class="mx-2 fw-medium">Images:</span>
                        <div>
                            @foreach (json_decode($runningJob->images, true) as $image)
                                <img src="{{ asset('storage/' . $image) }}" alt="Image"
                                    class="mx-1 img-thumbnail" width="100">
                            @endforeach
                        </div>
                    </li>
                @endif
                @if (!empty($runningJob->attachments))
                    <li class="mb-4 d-flex align-items-center">
                        <i class="bx bx-paperclip"></i>
                        <span class="mx-2 fw-medium">Attachments:</span>
                        <ul>
                            @foreach (json_decode($runningJob->attachments, true) as $attachment)
                                <li>
                                    <a href="{{ asset('storage/' . $attachment) }}" target="_blank">View
                                        Attachment</a>
                                </li>
                            @endforeach
                        </ul>
                    </li>
                @endif
            </ul>
        </div>

        <div class="app-chat-history">
            <div class="pb-1 chat-history-wrapper">
                <div class="chat-history-body">
                    <ul class="list-unstyled chat-history">
                        @foreach ($runningJob->conversations ?? [] as $conversation)
                            <li
                                class="chat-message {{ $conversation->user_id == auth()->id() ? 'chat-message-right' : 'chat-message-left' }}">
                                <div class="overflow-hidden d-flex">
                                    <div class="chat-message-wrapper flex-grow-1">
                                        <div class="chat-message-text">
                                            <h6><strong>{{ $conversation->user->name ?? 'Unknown' }}:</strong></h6>
                                            <p class="mb-0">{!! nl2br($conversation->description) !!}</p>
                                        </div>
                                        <div class="mt-1 text-muted">
                                            <small>{{ $conversation->created_at->format('h:i A, M d Y') }}</small>
                                        </div>
                                        @if (!empty($conversation->images))
                                            <div class="mt-2">
                                                @foreach (json_decode($conversation->images, true) as $image)
                                                    <a href="{{ asset('storage/' . $image) }}" target="_blank">
                                                        <img src="{{ asset('storage/' . $image) }}"
                                                            class="mx-1 img-thumbnail" width="100">
                                                    </a>
                                                @endforeach
                                            </div>
                                        @endif
                                        @if (!empty($conversation->attachments))
                                            <div class="mt-2">
                                                @foreach (json_decode($conversation->attachments, true) as $attachment)
                                                    <a href="{{ asset('storage/' . $attachment) }}" target="_blank"
                                                        class="btn btn-sm btn-outline-secondary">
                                                        View Attachment
                                                    </a>
                                                @endforeach
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </li>
                        @endforeach
                    </ul>
                </div>

                @if (true)
                    <div class="chat-history-footer">
                        <form id="sendConversationForm" method="POST" enctype="multipart/form-data"
                            action="{{ route('running_jobs.runningJobs.addConversation', $runningJob->id) }}">
                            @csrf
                            <input type="hidden" name="running_job_id" value="{{ $runningJob->id }}">
                            <input type="hidden" name="user_id" value="{{ auth()->id() }}">

                            <div class="my-2">
                                <label for="description" class="form-label">Comments</label>
                                <textarea name="description" class="form-control me-2 flex-grow-1"
                                    placeholder="Add a comments between the agents and office only" required rows="4"></textarea>
                            </div>

                            <div class="my-2 row">
                                <div class="col-sm-12">
                                    <label for="images" class="form-label">Upload Images</label>
                                    <input type="file" name="images[]" class="form-control" id="images"
                                        multiple accept="image/*">
                                </div>
                            </div>

                            <div class="my-2 row">
                                <div class="col-sm-12">
                                    <label for="attachments" class="form-label">Upload Attachments</label>
                                    <input type="file" name="attachments[]" class="form-control" id="attachments"
                                        multiple accept=".pdf">
                                </div>
                            </div>

                            <div class="text-end">
                                <button class="w-10 btn btn-primary">Send</button>
                            </div>
                        </form>
                    </div>
                @endif

            </div>
        </div>

        <div class="text-center">
            @if ($runningJob->quotation)
                <a target="_blank" href="{{ route('member-allocates.index') }}" class="btn btn-primary">Allocate
                    Chauffeur</a>
            @endif
            @if (
                $runningJob->quotation &&
                    $runningJob->quotation->memberAllocate &&
                    $runningJob->quotation->memberAllocate->driver_id)
                <a target="_blank"
                    href="{{ route('members.show', $runningJob->quotation->memberAllocate->driver_id) }}"
                    class="btn btn-primary">View Chauffeur </a>
            @endif
            @if (
                $runningJob->quotation &&
                    $runningJob->quotation->memberAllocate &&
                    $runningJob->quotation->memberAllocate->guide_id)
                <a target="_blank"
                    href="{{ route('members.show', $runningJob->quotation->memberAllocate->guide_id) }}"
                    class="btn btn-primary">View Chauffeur </a>
            @endif
            @if ($runningJob->quotation)
                <a target="_blank"
                    href="{{ route('quotations.documents.index', ['id' => $runningJob->quotation->reference_no ?? 0]) }}"
                    class="btn btn-primary">
                    Upload Tour Documents
                </a>
            @endif
        </div>
    </div>
