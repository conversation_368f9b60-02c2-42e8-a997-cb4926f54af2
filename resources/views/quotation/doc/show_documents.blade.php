@extends('layouts/contentNavbarLayout')

@section('title', 'Quotation Documents')

@section('content')
    <div class="container-xxl flex-grow-1 container-p-y">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Quotation Documents</h5>
            </div>
            <div class="card-body">
                <!-- Reference No Search -->
                <div class="mb-3">
                    <label for="reference_no" class="form-label">Enter Reference No.</label>
                    <input type="text" id="reference_no" class="form-control" placeholder="Enter Quotation Reference No">
                    <button class="mt-2 btn btn-primary" id="searchQuotation">Search</button>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h5 class="mt-4">Air Ticket Files</h5>
                        <div class="row" id="airTicketFilesList"></div>
                    </div>
                    <div class="col-md-6">
                        <h5 class="mt-4">Passport Files</h5>
                        <div class="row" id="passportFilesList"></div>
                    </div>

                    <!-- Display Hotels and Attractions -->
                    <div id="quotationDetails" style="display: none;">
                        <h5 class="mt-4">Hotels</h5>
                        <ul class="list-group" id="hotelList"></ul>

                        <h5 class="mt-4">Attractions</h5>
                        <ul class="list-group" id="attractionList"></ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Uploading Documents -->
    <div class="modal fade" id="uploadModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="uploadModalLabel">Upload Documents</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <h6 id="uploadItemName"></h6>
                    <form id="uploadForm" enctype="multipart/form-data">
                        @csrf
                        <input type="hidden" id="uploadItemId" name="item_id">
                        <input type="hidden" id="uploadItemType" name="item_type">

                        <div class="mb-3">
                            <label for="files" class="form-label">Upload Images/PDF</label>
                            <input type="file" id="files" name="files[]" class="form-control" multiple required
                                accept="image/*, .pdf">
                        </div>

                        <button type="submit" class="btn btn-primary">Upload</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('page-script')
    <script>
        $(document).ready(function() {


            // Fetch hotels and attractions based on Reference No.
            $('#searchQuotation').click(function() {
                let referenceNo = $('#reference_no').val();
                if (!referenceNo) {
                    alert('Please enter a Reference No.');
                    return;
                }
                $.ajax({
                    url: "{{ route('quotations.documents', '') }}?id=" + referenceNo,
                    method: "GET",
                    success: function(response) {
                        $('#hotelList').empty();
                        $('#attractionList').empty();
                        $('#airTicketFilesList').empty();
                        $('#passportFilesList').empty();

                        if (response.hotels.length > 0) {
                            response.hotels.forEach(hotel => {
                                console.log(hotel.documents);
                                let docsHtml = hotel.documents.length > 0 ?
                                    hotel.documents.map(doc =>
                                        `<a href="${doc.file_path}" target="_blank" class="btn btn-sm btn-secondary">View Document</a>`
                                    ).join(' ') :
                                    '<span class="text-muted">No documents</span>';

                                $('#hotelList').append(`
                                <li class="list-group-item">
                                    ${hotel.hotel.name} - ${docsHtml}
                                    <button class="btn btn-sm btn-primary float-end uploadBtn"
                                        data-id="${hotel.id}" data-name="${hotel.hotel.name}" data-type="hotel">
                                        Upload Documents
                                    </button>
                                </li>
                            `);
                            });
                        } else {
                            $('#hotelList').append(
                                '<li class="list-group-item">No hotels found.</li>');
                        }

                        if (response.attractions.length) {
                            response.attractions.forEach(attraction => {
                                let docsHtml = attraction.documents.length > 0 ?
                                    attraction.documents.map(doc =>
                                        `<a href="${doc.file_path}" target="_blank" class="btn btn-sm btn-secondary">View Document</a>`
                                    ).join(' ') :
                                    '<span class="text-muted">No documents</span>';

                                $('#attractionList').append(`
                                <li class="list-group-item">
                                    ${attraction.attraction.name} ${docsHtml}
                                    <button class="btn btn-sm btn-primary float-end uploadBtn"
                                        data-id="${attraction.id}" data-name="${attraction.attraction.name}" data-type="attraction">
                                        Upload Documents
                                    </button>
                                </li>
                            `);
                            });
                        } else {
                            $('#attractionList').append(
                                '<li class="list-group-item">No attractions found.</li>');
                        }

                        // Display Air Ticket Files
                        if (response.booking && response.booking.air_ticket_files && response
                            .booking.air_ticket_files.length > 0) {
                            response.booking.air_ticket_files.forEach((file, index) => {
                                let fileExtension = file.file_path.split('.').pop()
                                    .toLowerCase();
                                let isImage = ['jpg', 'jpeg', 'png', 'gif'].includes(
                                    fileExtension);
                                let isPdf = fileExtension === 'pdf';

                                let fileIcon = isImage ? 'bx-image' : (isPdf ?
                                    'bx-file-pdf' : 'bx-file');
                                let fileSize = (file.file_size / 1024).toFixed(2);

                                $('#airTicketFilesList').append(`
                                    <div class="col-md-6 col-lg-4 mb-3">
                                        <div class="card h-100">
                                            <div class="card-body d-flex flex-column">
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="bx ${fileIcon} me-2 text-primary" style="font-size: 1.5rem;"></i>
                                                    <div class="flex-grow-1">
                                                        <h6 class="card-title mb-1 text-truncate" title="${file.original_name}">
                                                            ${file.original_name}
                                                        </h6>
                                                        <small class="text-muted">${fileSize} KB</small>
                                                    </div>
                                                </div>
                                                <div class="mt-auto">
                                                    <a href="/storage/${file.file_path}" target="_blank"
                                                       class="btn btn-sm btn-outline-primary w-100">
                                                        <i class="bx bx-show me-1"></i> View File
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                `);
                            });
                        } else {
                            $('#airTicketFilesList').append(`
                                <div class="col-12">
                                    <div class="text-center text-muted py-4">
                                        <i class="bx bx-file-blank" style="font-size: 3rem;"></i>
                                        <p class="mt-2">No air ticket files uploaded</p>
                                    </div>
                                </div>
                            `);
                        }

                        // Display Passport Files
                        if (response.booking && response.booking.passport_files && response
                            .booking.passport_files.length > 0) {
                            response.booking.passport_files.forEach((file, index) => {
                                let fileExtension = file.file_path.split('.').pop()
                                    .toLowerCase();
                                let isImage = ['jpg', 'jpeg', 'png', 'gif'].includes(
                                    fileExtension);
                                let isPdf = fileExtension === 'pdf';

                                let fileIcon = isImage ? 'bx-image' : (isPdf ?
                                    'bx-file-pdf' : 'bx-file');
                                let fileSize = (file.file_size / 1024).toFixed(2);

                                $('#passportFilesList').append(`
                                    <div class="col-md-6 col-lg-4 mb-3">
                                        <div class="card h-100">
                                            <div class="card-body d-flex flex-column">
                                                <div class="d-flex align-items-center mb-2">
                                                    <i class="bx ${fileIcon} me-2 text-success" style="font-size: 1.5rem;"></i>
                                                    <div class="flex-grow-1">
                                                        <h6 class="card-title mb-1 text-truncate" title="${file.original_name}">
                                                            ${file.original_name}
                                                        </h6>
                                                        <small class="text-muted">${fileSize} KB</small>
                                                    </div>
                                                </div>
                                                <div class="mt-auto">
                                                    <a href="/storage/${file.file_path}" target="_blank"
                                                       class="btn btn-sm btn-outline-success w-100">
                                                        <i class="bx bx-show me-1"></i> View File
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                `);
                            });
                        } else {
                            $('#passportFilesList').append(`
                                <div class="col-12">
                                    <div class="text-center text-muted py-4">
                                        <i class="bx bx-file-blank" style="font-size: 3rem;"></i>
                                        <p class="mt-2">No passport files uploaded</p>
                                    </div>
                                </div>
                            `);
                        }

                        $('#quotationDetails').show();
                    },
                    error: function() {
                        alert('Quotation not found. Please check the Reference No.');
                    }
                });
            });

            // Show Upload Modal
            $(document).on('click', '.uploadBtn', function() {
                let itemId = $(this).data('id');
                let itemName = $(this).data('name');
                let itemType = $(this).data('type');

                $('#uploadItemId').val(itemId);
                $('#uploadItemName').text(`Upload Documents for ${itemName}`);
                $('#uploadItemType').val(itemType);
                $('#uploadModal').modal('show');
            });

            // Upload Documents via AJAX
            $('#uploadForm').submit(function(e) {
                e.preventDefault();

                let formData = new FormData(this);
                let itemType = $('#uploadItemType').val();
                let uploadUrl = itemType === 'hotel' ?
                    "{{ route('quotations.hotels.documents', '') }}/" + $('#uploadItemId').val() :
                    "{{ route('quotations.attractions.documents', '') }}/" + $('#uploadItemId').val();

                $.ajax({
                    url: uploadUrl,
                    method: "POST",
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function() {
                        alert('Documents uploaded successfully!');
                        $('#uploadModal').modal('hide');
                        $('#uploadForm')[0].reset();
                    },
                    error: function() {
                        alert('An error occurred while uploading documents.');
                    }
                });
            });

            const urlParams = new URLSearchParams(window.location.search);
            const refNo = urlParams.get('id');
            if (refNo) {
                $('#reference_no').val(refNo);
                $('#searchQuotation').click();
            }
        });
    </script>
@endsection
