@extends('layouts/contentNavbarLayout')

@section('title', 'Quotation Management')

@section('vendor-style')
    <link rel="stylesheet" href="https://cdn.datatables.net/2.1.5/css/dataTables.bootstrap5.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/searchpanes/2.3.2/css/searchPanes.bootstrap5.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/select/2.0.5/css/select.bootstrap5.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/2.1.5/css/dataTables.dataTables.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/select/2.0.5/css/select.dataTables.css">
@endsection

@section('vendor-script')
    <script src="https://code.jquery.com/jquery-3.7.1.js"></script>
    <script src="https://cdn.datatables.net/2.1.5/js/dataTables.js"></script>
    <script src="https://cdn.datatables.net/2.1.5/js/dataTables.bootstrap5.js"></script>
    <script src="https://cdn.datatables.net/buttons/3.1.2/js/dataTables.buttons.js"></script>
    <script src="https://cdn.datatables.net/buttons/3.1.2/js/buttons.bootstrap5.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/3.1.2/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/3.1.2/js/buttons.print.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/3.1.2/js/buttons.colVis.min.js"></script>
@endsection

@section('page-style')
    <link rel="stylesheet" href="{{ asset('assets/css/data-table.css') }}">
@endsection

@section('content')
    <div class="col-xl-12 col-lg-7 order-0 order-md-1">
        <!-- Quotation table -->
        <div class="mb-6 card">
            <div class="card-header border-bottom">
                <h5 class="my-3 mb-0 card-title d-inline-block">My Quotations</h5>
                <div class="mb-3 row">
                    <div class="mb-3 col-3">
                        <label for="tourDaysFilter">Tour Days</label>
                        <select id="tourDaysFilter" class="form-select">
                            <option value="">All</option>
                            <option value="1">1 Day</option>
                            <option value="2">2 Days</option>
                            <option value="3">3 Days</option>
                            <option value="4">4 Days</option>
                            <option value="5">5 Days</option>
                            <option value="6">6 Days</option>
                            <option value="7">7 Days</option>
                            <option value="8">8 Days</option>
                            <option value="9">9 Days</option>
                            <option value="10">10+ Days</option>
                        </select>
                    </div>

                    <div class="mb-3 col-3">
                        <label for="paxFilter">PAX (Adults + Children)</label>
                        <input type="number" id="paxFilter" class="form-control" placeholder="Total PAX">
                    </div>

                    <div class="mb-3 col-3">
                        <label for="attractionTypeFilter">Attraction Type</label>
                        <select id="attractionTypeFilter" class="form-select">
                            <option value="">All Types</option>
                            @foreach ($attractionTypes as $type)
                                <option value="{{ $type->id }}">{{ $type->type }}</option>
                            @endforeach
                        </select>
                    </div>

                    <div class="mb-3 col-3">
                        <label for="arrivalMonthFilter">Arrival Month</label>
                        <input type="month" id="arrivalMonthFilter" class="form-control">
                    </div>

                    <div class="mb-3 col-3">
                        <label for="attractionNameFilter">Attraction Name</label>
                        <input type="text" id="attractionNameFilter" class="form-control"
                            placeholder="Search by Attraction">
                    </div>

                    <div class="mb-3 col-3">
                        <label for="hotelClassFilter">Hotel Categories</label>
                        <select id="hotelClassFilter" class="form-select">
                            <option value="">All Categories</option>
                            @foreach ($hotelClasses as $class)
                                <option value="{{ $class->id }}">{{ $class->class }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
            </div>

            <div class="card-datatable table-responsive">
                <table class="table datatables-quotations border-top">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Quotation No.</th>
                            <th>Reference No.</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Author</th>
                            <th>Total Amount</th>
                            <th>Version</th>
                            <th>Enabled/Disabled</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
        <!-- /Quotation table -->
    </div>

@endsection

@section('page-script')
    <script src="{{ asset('assets/js/admin.js') }}"></script>
    <script>
        window.userRoles = @json(Auth::user()->roles->pluck('name')->toArray());
    </script>
    <script>
        function copyPrivateLink(id) {
            const privateLink = `{{ url('quotations') }}/${id}`;

            // Use navigator.clipboard if available
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(privateLink).then(() => {
                    alert('Private link copied to clipboard');
                    window.open(privateLink, '_blank'); // Opens in a new tab
                }).catch(err => {
                    console.error('Failed to copy private link:', err);
                });
            } else {
                // Fallback method for unsupported browsers
                const textArea = document.createElement("textarea");
                textArea.value = privateLink;
                textArea.style.position = "fixed"; // Prevents scrolling to the bottom
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();

                try {
                    const successful = document.execCommand('copy');
                    alert(successful ? 'Private link copied to clipboard' : 'Failed to copy private link');
                    window.open(privateLink, '_blank'); // Opens in a new tab
                } catch (err) {
                    console.error('Fallback: Failed to copy private link', err);
                }

                document.body.removeChild(textArea);
            }
        }

        function openPrivateLink(id) {
            const privateLink = `{{ url('admin/quotations/preview') }}/${id}`;
            window.open(privateLink, '_blank'); // Opens in a new tab
        }

        $(document).ready(function() {
            $('#tourDaysFilter, #paxFilter, #attractionTypeFilter, #arrivalMonthFilter, #attractionNameFilter, #hotelClassFilter')
                .on('change keyup', function() {
                    $('.datatables-quotations').DataTable().ajax.reload();
                });

            $('.datatables-quotations').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: "{{ route('quotations.getList') }}",
                    data: function(d) {
                        d.tour_days = $('#tourDaysFilter').val();
                        d.pax = $('#paxFilter').val();
                        d.attraction_type = $('#attractionTypeFilter').val();
                        d.arrival_month = $('#arrivalMonthFilter').val();
                        d.attraction_name = $('#attractionNameFilter').val();
                        d.hotel_class = $('#hotelClassFilter').val();
                    }
                },
                order: [
                    [0, 'desc']
                ],
                ordering: true,
                columns: [{
                        data: 'id',
                        name: 'id'
                    },
                    {
                        data: 'quotation_no',
                        name: 'quotation_no'
                    },
                    {
                        data: 'reference_no',
                        name: 'reference_no'
                    },
                    {
                        data: 'created_at',
                        name: 'created_at'
                    },
                    {
                        data: 'save_type',
                        name: 'save_type',
                        render: function(data, type, row) {
                            const statusBadgeClass = data === 'Saved' ? 'bg-warning' : 'bg-success';
                            return `<span class="badge ${statusBadgeClass}">${data}</span>`;
                        }
                    },
                    {
                        data: 'user',
                        name: 'user'
                    },
                    {
                        data: 'total',
                        name: 'total'
                    },
                    {
                        data: 'versions',
                        name: 'versions',
                        orderable: false,
                        searchable: false,
                        render: function(data, type, row) {
                            if (!Array.isArray(data)) return '';

                            // Total number of versions
                            const total = data.length;

                            // Map versions so latest shows "Version 01", next "Version 02", etc.
                            let options = data.map((v, index) => {
                                // Add colored dot based on disabled status
                                const statusDot = v.disabled ?
                                    '<span style="color: #ff0000;">&#128308;</span>' :
                                    // Red dot for disabled
                                    '<span style="color: #00b300;">&#128994;</span>'; // Green dot for enabled

                                return `<option value="${v.id}" data-id="${v.id}" data-quotation_no="${v.quotation_no}" data-token="${v.token}" data-disabled="${v.disabled}" data-reference_no="${v.reference_no || ''}" data-save_type="${v.save_type}" data-user="${v.user}" data-total="${v.total}">
                                    Version ${String(total - index).padStart(2, '0')} ${statusDot}
                                </option>`;
                            }).join('');

                            return `<select class="form-select form-select-sm version-select" data-original-id="${row.id}" data-original-quotation_no="${row.quotation_no}">
                                ${options}
                            </select>`;
                        }
                    },
                    {
                        data: 'disabled',
                        name: 'disabled',
                        orderable: true,
                        searchable: true,
                        render: function(data, type, row) {
                            const checked = !data ? 'checked' : '';
                            const status = !data ? 'Enabled' : 'Disabled';
                            const statusClass = !data ? 'text-success' : 'text-danger';

                            return `
                                <div class="form-check form-switch"  style="transform: scale(0.85); transform-origin: left;">
                                    <input class="form-check-input toggle-quotation-status" type="checkbox"
                                        data-id="${row.quotation_no}"
                                        data-version-id="${row.current_version_id}"
                                        ${checked}>
                                    <label class="form-check-label ${statusClass}"  style="font-size: 0.85rem;">${status}</label>
                                </div>
                            `;
                        }
                    },
                    {
                        data: null,
                        name: 'Actions',
                        orderable: false,
                        searchable: false,
                        render: function(data, type, row) {
                            let userRoles = window.userRoles || [];

                            return `
                                <div class="dropdown">
                                    <button type="button" class="p-0 btn dropdown-toggle hide-arrow" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bx bx-dots-vertical-rounded"></i>
                                    </button>
                                    <div class="dropdown-menu">
                                        ${userRoles.includes('Super Admin') || userRoles.includes('Admin') || userRoles.includes('Accountant') || userRoles.includes('Travel Consultant') ? `
                                                                                                                                                                                                        <a class="dropdown-item amendment-quotation" href="javascript:void(0);">
                                                                                                                                                                                                            <i class="bx bx-edit-alt me-1"></i> Edit
                                                                                                                                                                                                        </a>
                                                                                                                                                                                                        <a class="dropdown-item send-vouchers" data-url-base="{{ url('admin/quotation/send/custom-send-hotel-vouchers') }}" href="#">
                                                                                                                                                                                                            <i class="bx bx-paper-plane me-1"></i> Send Vouchers
                                                                                                                                                                                                        </a>
                                                                                                                                                                                                        <a class="dropdown-item pnl-quotation" data-url-base="{{ url('admin/quotation/custom-pnl-review') }}" href="#">
                                                                                                                                                                                                            <i class="bx bx-chart me-1"></i> PNL
                                                                                                                                                                                                        </a>` : ''}
                                        ${row.token ? `
                                                                                                                                                                                                        <a class="dropdown-item confirmation-report" data-url-base="{{ url('admin/quotation/custom-confirmation-report') }}" href="#">
                                                                                                                                                                                                            <i class="bx bx-file me-1"></i> Hotel Confirmation Report
                                                                                                                                                                                                        </a>
                                                                                                                                                                                                        <a class="dropdown-item preview-version" href="javascript:void(0);">
                                                                                                                                                                                                            <i class="bx bx-link-alt me-1"></i> Preview
                                                                                                                                                                                                        </a>
                                                                                                                                                                                                        <a class="dropdown-item copy-version" href="javascript:void(0);">
                                                                                                                                                                                                            <i class="bx bx-copy-alt me-1"></i> Copy Private Link
                                                                                                                                                                                                        </a>` : ''}
                                        ${userRoles.includes('Super Admin') || userRoles.includes('Admin') ? `
                                                                                                                                                                                                        <a class="dropdown-item delete-quotation" href="javascript:void(0);">
                                                                                                                                                                                                            <i class="bx bx-trash me-1"></i> Delete
                                                                                                                                                                                                        </a>` : ''}
                                    </div>
                                </div>
                            `;
                        }
                    }
                ],
                createdRow: function(row, data, dataIndex) {
                    // Check the 'save_type' value and apply the appropriate class
                    if (data.save_type === 'Confirmed') {
                        $(row).addClass('table-success'); // Green for confirmed
                    } else if (data.save_type === 'Saved') {
                        $(row).addClass('table-warning'); // Yellow for saved
                    }
                },
                pageLength: 25,
                lengthMenu: [10, 25, 50],
                buttons: ['copy', 'excel', 'pdf', 'colvis']
            });
            /**
            $(document).on('click', '.amendment-quotation', function() {
                NProgress.start();
                let parentRow = $(this).closest('tr');
                let selectedOption = parentRow.find('.version-select option:selected');

                let id = selectedOption.data('id');
                let quotation_no = selectedOption.data('quotation_no');

                let data = {
                    "id": id,
                    "quotation_no": quotation_no
                };

                reviewQuotation(data, function(response) {
                    alert(123);
                    window.location.href = response.redirect;
                    NProgress.done();
                });
            });

            $(document).on('click', '.delete-quotation', function() {
                let id = $(this).data('id');
                var quotation_no = $(this).data('quotation_no');
                Swal.fire({
                    title: "Are you sure?",
                    text: "You won't be able to revert this!",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#d33",
                    cancelButtonColor: "#3085d6",
                    confirmButtonText: "Yes, delete it!"
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: "{{ route('quotation.delete') }}",
                            type: "POST",
                            data: {
                                _token: "{{ csrf_token() }}",
                                id: id,
                                id: quotation_no
                            },
                            success: function(response) {
                                Swal.fire("Deleted!", "The quotation has been deleted.",
                                    "success");
                                $('.datatables-quotations').DataTable().ajax
                                    .reload(); // Refresh DataTable
                            },
                            error: function(xhr) {
                                Swal.fire("Error!", "An error occurred while deleting.",
                                    "error");
                            }
                        });
                    }
                });
            });
            **/
            $(document).on('click', '.dropdown-menu a', function(e) {
                const $row = $(this).closest('tr');
                const $selected = $row.find('.version-select').find(':selected');
                const id = $selected.data('id');
                const token = $selected.data('token');
                const quotation_no = $selected.data('quotation_no');

                if ($(this).hasClass('send-vouchers') ||
                    $(this).hasClass('confirmation-report') ||
                    $(this).hasClass('pnl-quotation')) {

                    const baseUrl = $(this).data('url-base');
                    const url = `${baseUrl}/${id}/${quotation_no}`;
                    window.open(url, '_blank'); // open in new tab
                    e.preventDefault();
                }

                if ($(this).hasClass('amendment-quotation')) {
                    NProgress.start();
                    reviewQuotation({
                        id,
                        quotation_no
                    }, function(response) {
                        window.location.href = response.redirect;
                        NProgress.done();
                    });
                    e.preventDefault();
                }

                if ($(this).hasClass('delete-quotation')) {
                    Swal.fire({
                        title: "Are you sure?",
                        text: "You won't be able to revert this!",
                        icon: "warning",
                        showCancelButton: true,
                        confirmButtonColor: "#d33",
                        cancelButtonColor: "#3085d6",
                        confirmButtonText: "Yes, delete it!"
                    }).then((result) => {
                        if (result.isConfirmed) {
                            $.ajax({
                                url: "{{ route('quotation.delete') }}",
                                type: "POST",
                                data: {
                                    _token: "{{ csrf_token() }}",
                                    id: id,
                                    quotation_no: quotation_no
                                },
                                success: function(response) {
                                    Swal.fire("Deleted!",
                                        "The quotation has been deleted.", "success"
                                    );
                                    $('.datatables-quotations').DataTable().ajax
                                        .reload();
                                },
                                error: function(xhr) {
                                    Swal.fire("Error!",
                                        "An error occurred while deleting.", "error"
                                    );
                                }
                            });
                        }
                    });
                    e.preventDefault();
                }


                if ($(this).hasClass('preview-version')) {
                    const privateLink = `{{ url('admin/quotations/preview') }}/${token}`;
                    window.open(privateLink, '_blank');
                    e.preventDefault();
                }

                if ($(this).hasClass('copy-version')) {
                    const privateLink = `{{ url('quotations') }}/${token}`;

                    if (navigator.clipboard && navigator.clipboard.writeText) {
                        navigator.clipboard.writeText(privateLink).then(() => {
                            alert('Private link copied to clipboard');
                            window.open(privateLink, '_blank');
                        }).catch(err => {
                            console.error('Failed to copy private link:', err);
                        });
                    } else {
                        const textArea = document.createElement("textarea");
                        textArea.value = privateLink;
                        textArea.style.position = "fixed";
                        document.body.appendChild(textArea);
                        textArea.focus();
                        textArea.select();

                        try {
                            const successful = document.execCommand('copy');
                            alert(successful ? 'Private link copied to clipboard' :
                                'Failed to copy private link');
                            window.open(privateLink, '_blank');
                        } catch (err) {
                            console.error('Fallback: Failed to copy private link', err);
                        }

                        document.body.removeChild(textArea);
                    }

                    e.preventDefault();
                }
            });

            // Handle toggle quotation status
            $(document).on('change', '.toggle-quotation-status', function() {
                const $row = $(this).closest('tr');
                const $select = $row.find('.version-select');
                const $selected = $select.find(':selected');
                const quotationId = $(this).data('id');
                const versionId = $selected.data('id'); // Get version ID directly from selected option
                const isEnabled = $(this).prop('checked');
                const statusLabel = $(this).siblings('label');

                $.ajax({
                    url: "{{ route('quotations.toggleStatus') }}",
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        quotation_id: quotationId,
                        version_id: versionId,
                        disabled: !isEnabled
                    },
                    success: function(response) {
                        if (response.success) {
                            // Update the label
                            if (isEnabled) {
                                statusLabel.text('Enabled').removeClass('text-danger').addClass(
                                    'text-success');

                                // Update the dot in the select option
                                $selected.data('disabled', false);
                                const optionText = $selected.text().replace('🔴', '🟢');
                                $selected.html(optionText.replace(/🟢?$/,
                                    '<span style="color: #00b300;">&#128994;</span>'));
                            } else {
                                statusLabel.text('Disabled').removeClass('text-success')
                                    .addClass('text-danger');

                                // Update the dot in the select option
                                $selected.data('disabled', true);
                                const optionText = $selected.text().replace('🟢', '🔴');
                                $selected.html(optionText.replace(/🔴?$/,
                                    '<span style="color: #ff0000;">&#128308;</span>'));
                            }

                            // Show success message
                            Swal.fire({
                                title: 'Success!',
                                text: response.message,
                                icon: 'success',
                                customClass: {
                                    confirmButton: 'btn btn-primary'
                                },
                                buttonsStyling: false
                            });
                        }
                    },
                    error: function(error) {
                        console.error('Error toggling quotation status:', error);

                        // Revert the checkbox state
                        $(this).prop('checked', !isEnabled);

                        // Show error message
                        Swal.fire({
                            title: 'Error!',
                            text: 'Failed to update quotation status.',
                            icon: 'error',
                            customClass: {
                                confirmButton: 'btn btn-primary'
                            },
                            buttonsStyling: false
                        });
                    }
                });
            });

            // Update version-specific toggle when version changes
            $(document).on('change', '.version-select', function() {
                const selectedOption = $(this).find('option:selected');
                const versionId = selectedOption.data('id');
                const isDisabled = selectedOption.data('disabled') === true || selectedOption.data(
                    'disabled') === 'true';
                const referenceNo = selectedOption.data('reference_no');
                const saveType = selectedOption.data('save_type');
                const user = selectedOption.data('user');
                const total = selectedOption.data('total');
                const token = selectedOption.data('token');

                const row = $(this).closest('tr');
                const toggleCheckbox = row.find('.toggle-quotation-status');
                const statusLabel = toggleCheckbox.siblings('label');

                // Update the checkbox state based on the selected version's disabled status
                toggleCheckbox.prop('checked', !isDisabled);

                // Update the disabled status label
                if (!isDisabled) {
                    statusLabel.text('Enabled').removeClass('text-danger').addClass('text-success');
                } else {
                    statusLabel.text('Disabled').removeClass('text-success').addClass('text-danger');
                }

                // Update the data-version-id attribute
                toggleCheckbox.attr('data-version-id', versionId);

                // Update visible cell content directly
                const cells = row.find('td');

                // Update reference_no (column index 2)
                $(cells[2]).text(referenceNo || '');

                // Update status (save_type) (column index 4)
                const statusBadgeClass = saveType === 'Saved' ? 'bg-warning' : 'bg-success';
                $(cells[4]).html(`<span class="badge ${statusBadgeClass}">${saveType}</span>`);

                // Update author (user) (column index 5)
                $(cells[5]).text(user);

                // Update total (column index 6)
                $(cells[6]).text(total);

                // Update the underlying DataTable row data for consistency
                const rowData = quotationTable.row(row).data();
                if (rowData) {
                    rowData.reference_no = referenceNo || '';
                    rowData.save_type = saveType;
                    rowData.user = user;
                    rowData.total = total;
                    rowData.token = token;
                }
            });
        });
    </script>
@endsection
