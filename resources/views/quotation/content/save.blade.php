@include('quotation.partials.invoice', [
    'quotation' => $quotation,
    'rates' => $rates,
    'classes' => '',
    'showBreakDown' => $showBreakDown,
    'showSupplement' => false,
    'privateLink' => $privateLink,
])


<div class="mb-4 content-header @if (hasAnyRole(['Foreign Agent', 'Foreign Agency'])) d-none @endif">
    <h6 class="mb-0">Mark Ups</h6>
</div>

<div class="mb-4 row g-3 mark-ups @if (hasAnyRole(['Foreign Agent', 'Foreign Agency'])) d-none @endif">
    <div class="col-sm-4">
        <label class="form-label" for="start">Mark Up Type</label>
        <select id="markup_type" name="markup_type" class="form-select select2" aria-placeholder="Select Markup Type">
            <option value="1" @if (isset($quotation['markup_type']) && $quotation['markup_type'] == 1) selected @endif>
                Percentage
            </option>
            <option value="2" @if (isset($quotation['markup_type']) && $quotation['markup_type'] == 2) selected @endif>
                @if (isset($quotation['currency']) && $quotation['currency'] == 'EURO')
                    Amount (EURO)
                @else
                    Amount (USD)
                @endif
            </option>
        </select>
    </div>
    <div class="col-sm-4">
        <label class="form-label" for="adult_markup">Adult Per Person Mark Up</label>
        <input type="text" class="form-control" name="adult_markup" id="adult_markup"
            value="{{ $quotation['adult_markup'] ?? 0 }}">
    </div>
    <div class="col-sm-4">
        <label class="form-label" for="child_markup">Child Per Person Mark Up</label>
        <input type="text" class="form-control" name="child_markup" id="child_markup"
            value="{{ $quotation['child_markup'] ?? 0 }}">
    </div>
</div>

<div class="mb-4 row g-3 mark-ups @if (hasAnyRole(['Foreign Agent', 'Foreign Agency'])) d-none @endif">
    <div class="col-sm-3">
        <label class="form-label" for="single_markup">Single Round Up</label>
        <input type="text" class="form-control" name="single_markup" id="single_markup"
            value="{{ $quotation['single_markup'] ?? 0 }}">
    </div>
    <div class="col-sm-3">
        <label class="form-label" for="double_markup">Double Round Up</label>
        <input type="text" class="form-control" name="double_markup" id="double_markup"
            value="{{ $quotation['double_markup'] ?? 0 }}">
    </div>
    <div class="col-sm-3">
        <label class="form-label" for="triple_markup">Triple Round Up</label>
        <input type="text" class="form-control" name="triple_markup" id="triple_markup"
            value="{{ $quotation['triple_markup'] ?? 0 }}">
    </div>
    <div class="col-sm-3">
        <label class="form-label" for="quadruple_markup">Quadruple Round Up</label>
        <input type="text" class="form-control" name="quadruple_markup" id="quadruple_markup"
            value="{{ $quotation['quadruple_markup'] ?? 0 }}">
    </div>
</div>

<div class="mb-4 content-header @if (hasAnyRole(['Foreign Agent', 'Foreign Agency'])) d-none @endif">
    <h6 class="mb-0">Discount</h6>
</div>

<div class="mb-4 row g-3 mark-ups @if (hasAnyRole(['Foreign Agent', 'Foreign Agency'])) d-none @endif">
    <div class="col-sm-6">
        <label class="form-label" for="discount_type">Discount Type</label>
        <select id="discount_type" name="discount_type" class="form-select select2"
            aria-placeholder="Select Discount Type">
            <option value="1" @if (isset($quotation['discount_type']) && $quotation['discount_type'] == 1) selected @endif>
                Percentage
            </option>
            <option value="2" @if (isset($quotation['discount_type']) && $quotation['discount_type'] == 2) selected @endif>
                Amount (USD)
            </option>
        </select>
    </div>
    <div class="col-sm-6">
        <label class="form-label" for="discount">Per Person Discount</label>
        <input type="text" class="form-control" name="discount" id="discount"
            value="{{ $quotation['discount'] ?? 0 }}">
    </div>
</div>

<div class="col-12 d-flex justify-content-between">
    <button class="btn btn-outline-secondary btn-prev">
        <i class="bx bx-left-arrow-alt bx-sm ms-sm-n2 me-sm-2"></i>
        <span class="align-middle d-sm-inline-block d-none">Previous</span>
    </button>
    <button class="btn btn-success btn-outline-done">
        <i class="bx bx-save bx-sm ms-sm-n2 me-sm-2"></i>
        <span class="align-middle d-sm-inline-block d-none">Save</span>
    </button>
    <div>
        @if (!isset($quotation['save_type']) || (isset($quotation['save_type']) && $quotation['save_type'] == 'save'))
            <button class="btn btn-warning btn-save">
                <i class="bx bx-save bx-sm ms-sm-n2 me-sm-2"></i>
                <span class="align-middle d-sm-inline-block d-none">Ready to Send Client</span>
            </button>
        @endif

        <button class="btn btn-primary btn-confirm">
            <i class="bx bx-certification bx-sm ms-sm-n2 me-sm-2"></i>
            <span class="align-middle d-sm-inline-block d-none">Confirm Quotation</span>
        </button>
    </div>
</div>
