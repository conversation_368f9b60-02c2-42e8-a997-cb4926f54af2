@extends('layouts/contentNavbarLayout')

@section('title', 'Quotation Create')

@section('vendor-style')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bs-stepper/dist/css/bs-stepper.min.css">
    <link rel="stylesheet" href="{{ asset('assets/css/bs-stepper.css') }}">

@endsection

@section('vendor-script')
    <script src="https://cdn.jsdelivr.net/npm/bs-stepper/dist/js/bs-stepper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>


@endsection

@section('page-style')
    <link rel="stylesheet" href="{{ asset('assets/css/chat.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/quotation.css') }}">
@endsection

@section('content')
    @if ($quotation)
        @include('quotation.content.partials.header', [
            'quotation' => $quotation,
            'dates' => $dates,
            'hotels' => $hotels,
            'rates' => $rates,
        ])

        @include('quotation.content.partials.basic', [
            'quotation' => $quotation,
            'dates' => $dates,
            'hotels' => $hotels,
            'rates' => $rates,
        ])

        <div class="row">
            <div class="col-12">
                <div class="mt-2 bs-stepper vertical wizard-modern wizard-modern-vertical">
                    <div class="bs-stepper-header">
                        @foreach ($dates as $index => $date)
                            <div class="step crossed @if ($index == 1) active @endif"
                                data-target="#day-{{ $date['day'] }}">
                                <button type="button" class="step-trigger" aria-selected="true"
                                    @if ($index != 1 && !isset($quotation['days'][$index])) disabled @endif>
                                    <span class="bs-stepper-circle">{{ $date['day'] }}</span>
                                    <span class="bs-stepper-label">
                                        <span class="bs-stepper-title">{{ $date['date'] }}</span>
                                        <span class="bs-stepper-subtitle">Day {{ $date['day'] }}</span>
                                    </span>
                                </button>
                            </div>
                            <div class="line"></div>
                        @endforeach
                        <div class="step crossed" data-target="#summery">
                            <button type="button" class="step-trigger" aria-selected="true"
                                @if (!isset($quotation['days']) || count($dates) != count($quotation['days'])) disabled @endif>
                                <span class="bs-stepper-circle">S</span>
                                <span class="bs-stepper-label">
                                    <span class="bs-stepper-title">Summery</span>
                                    <span class="bs-stepper-subtitle">Quotation</span>
                                </span>
                            </button>
                        </div>
                        <div class="step crossed" data-target="#save">
                            <button type="button" class="step-trigger" aria-selected="true"
                                @if (!isset($quotation['days']) || count($dates) != count($quotation['days'])) disabled @endif>
                                <span class="bs-stepper-circle">S</span>
                                <span class="bs-stepper-label">
                                    <span class="bs-stepper-title">Save</span>
                                    <span class="bs-stepper-subtitle">Quotation</span>
                                </span>
                            </button>
                        </div>
                        <div class="step crossed confirm" data-target="#confirm">
                            <button type="button" class="step-trigger" aria-selected="true" disabled>
                                <span class="bs-stepper-circle">C</span>
                                <span class="bs-stepper-label">
                                    <span class="bs-stepper-title">Confirm</span>
                                    <span class="bs-stepper-subtitle">Quotation</span>
                                </span>
                            </button>
                        </div>
                        <div class="line"></div>
                    </div>
                    <div class="bs-stepper-content quotation-form">
                        <!-- Account Details -->
                        @include('quotation.content.days', [
                            'quotation' => $quotation,
                            'dates' => $dates,
                            'hotels' => $hotels,
                            'rates' => $rates,
                            'languages' => $languages,
                            'positions' => $positions,
                            'types' => $types,
                            'durations' => $durations,
                            'categories' => $categories,
                            'pax' => $pax,
                            'runningJob' => $runningJob,
                            'privateLink' => $privateLink,
                        ])
                    </div>
                </div>
            </div>
        </div>
    @endif
@endsection

@section('page-script')
    <script src="//cdn.ckeditor.com/4.22.1/standard/ckeditor.js"></script>
    <script>
        var hasRestrictedRole = @json(hasAnyRole(['Foreign Agent', 'Foreign Agency']));
    </script>
    <script src="{{ asset('assets/js/quotation.js') }}"></script>
    <script>
        var hotel_meals = {!! json_encode(config('custom.hotel_meals')) !!};
        $(document).ready(function() {
            $(document).on('mouseenter', '[data-bs-toggle="popover"]', function() {
                $(this).popover({
                    trigger: 'hover',
                    html: true
                }).popover('show');
            }).on('mouseleave', '[data-bs-toggle="popover"]', function() {
                $(this).popover('hide');
            });

            // Initialize the stepper
            var stepper = new Stepper($('.wizard-modern-vertical')[0], {
                linear: false, // Set to true if you want linear navigation (one step at a time)
                animation: true // Enable smooth transitions between steps
            });

            var stepsState = [];

            // Step 2: Loop through all steps and populate the stepsState array
            $('.bs-stepper-header .step').each(function(index) {
                var target = $(this).attr('data-target'); // Get the data-target value
                stepsState.push({
                    index: index,
                    target: target,
                    disabled: $(this).find('button').attr('disabled') ? true : false
                });
            });

            $('.wizard-modern-vertical')[0].addEventListener('shown.bs-stepper', function(event) {
                NProgress.start();
                var $targetStep = $(event.target);
                $element = $targetStep.find('.dstepper-block.active');
                var day = $element.attr('id').replace('day-', '');
                getDayDetails(day, function(response) {
                    $element.html(response);
                    anableSelect2();

                    $('.sub.small').hide(); // summery page expanded
                    NProgress.done();
                });
            });

            $(document).on('click', '.btn-next', function() {
                let $currentStep = $(this).closest('.dstepper-block');

                // Trigger save first and proceed only if successful
                $currentStep.find('.btn-success').trigger('click', {
                    callback: function(success) {
                        if (success) {
                            setTimeout(() => {
                                stepper.next();
                            }, 500);
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Step not saved!',
                                text: 'Please resolve the errors before proceeding.',
                                confirmButtonText: 'OK'
                            });
                        }
                    }
                });
            });

            $(document).on('click', '.btn-prev', function() {
                stepper.previous();
            });

            $(document).on('click', '.dstepper-block .btn-success', function(e, data) {
                e.preventDefault(); // Prevent form submission
                NProgress.start();

                // Get all input, select, and textarea fields from the form
                var formData = {};
                $dstepperBlock = $(this).closest('.dstepper-block');
                $dstepperBlock.find('input, select, textarea').each(function() {
                    var name = $(this).attr('name');
                    var value = $(this).val();
                    formData[name] = value;
                });

                if (typeof data !== 'undefined' && data !== null && typeof data.save_type !== 'undefined') {
                    formData['save_type'] = data.save_type;
                }

                // Convert formData to JSON string
                var jsonData = JSON.stringify(formData);

                saveSession(jsonData, function(response, status) {
                    console.log(response, status);
                    if (status !== 'error') {
                        id = $dstepperBlock.attr('id');
                        var currentStepIndex = stepsState.findIndex(step => step.target == "#" +
                            id);
                        if (currentStepIndex <= stepsState.length - 1) {
                            stepsState[currentStepIndex].disabled = false; // Enable the next step
                            updateStepState(stepsState);
                        }

                        // load tab again
                        $element = $dstepperBlock;
                        var day = $element.attr('id').replace('day-', '');
                        getDayDetails(day, function(response) {
                            $element.html(response);
                            anableSelect2();

                            $('.sub.small').hide(); // summery page expanded
                            NProgress.done();

                            // Execute callback function if provided
                            if (data && typeof data.callback === 'function') {
                                data.callback(true); // Pass success state
                            }
                        });
                    } else {
                        // Handle failure response
                        NProgress.done();
                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: response.message ||
                                'Something went wrong. Please try again.',
                            confirmButtonText: 'OK'
                        });

                        // Execute callback function with failure
                        if (data && typeof data.callback === 'function') {
                            data.callback(false); // Pass failure state
                        }
                    }
                });

            });

            $(document).on('click', '.dstepper-block .btn-save', function(e) {
                $(this).closest('.dstepper-block').find('.btn-success').trigger('click', {
                    save_type: 'save',
                    callback: function(success) {
                        if (success) {
                            $('.bs-stepper-header .confirm').attr('data-target',
                                '#save-confirm');
                            $('.bs-stepper-content .confirm').attr('id', 'save-confirm');
                            setTimeout(() => {
                                stepper.next();
                            }, 500);
                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'Step not saved!',
                                text: 'Please resolve the errors before proceeding.',
                                confirmButtonText: 'OK'
                            });
                        }
                    }
                });

            });

            $(document).on('click', '.dstepper-block .btn-confirm', function(e) {
                $(this).closest('.dstepper-block').find('.btn-success').trigger('click', {
                    save_type: 'confirm'
                });
                $('.bs-stepper-header .confirm').attr('data-target', '#confirm-confirm');
                $('.bs-stepper-content .confirm').attr('id', 'confirm-confirm');
                setTimeout(() => {
                    stepper.next();
                }, 2000);
            });
            // DESTINATION SECTION ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

            anableSelect2();

            function anableSelect2() {
                $('.places_select2').each(function() {
                    id = $(this).attr('id');
                    $('#' + id).select2({
                        placeholder: 'Select Place',
                        minimumInputLength: 2,
                        ajax: {
                            url: '/admin/services/suggestions', // Replace with your actual API endpoint
                            dataType: 'json',
                            delay: 250,
                            data: function(params) {
                                return {
                                    q: params.term, // The search term
                                    type: 'place',
                                    country: {{ $quotation['country'] ?? 62 }},
                                    sub_type: 1,
                                };
                            },
                            processResults: function(data) {
                                // Map the data from your server response to select2 format
                                return {
                                    results: $.map(data, function(item) {
                                        return {
                                            id: item.id,
                                            text: item.name
                                        };
                                    })
                                };
                            },
                            cache: true
                        }
                    });
                });
            }

            $(document).on('select2:select', '.places_select2', function(e) {
                const $dayContainer = $(this).closest('.dstepper-block');
                const start = $dayContainer.find('select[name="start"]').val();
                const end = $dayContainer.find('select[name="end"]').val();

                // Collect all extra places in order
                const extraPlaces = [];
                $dayContainer.find('select[name^="extra_places"]').each(function() {
                    const val = $(this).val();
                    if (val) extraPlaces.push(val);
                });

                const fullRoute = [start, ...extraPlaces, end];

                getTotalDistance(fullRoute, function(err, distance) {
                    if (!err) {
                        let totalDistance = distance / 1000; // Convert meters to kilometers

                        // Add attraction distances if any
                        $dayContainer.find(".attractions .attraction-data").each(function() {
                            let attractionData = $(this).val();
                            if (attractionData !== '') {
                                let parsedData = JSON.parse(attractionData);
                                totalDistance += parseFloat(parsedData.distance) || 0;
                            }
                        });

                        $dayContainer.find('.transport #distance').val(totalDistance);
                    } else {
                        console.error(err);
                    }
                });
            });

            $(document).on('click', '.add-mid-box', function() {
                let $dayWrapper = $(this).closest('.day-details'); // scoped to day wrapper
                let boxIndex = $dayWrapper.find('select[name^="extra_places["]').length;

                let day = $dayWrapper.find('input.day').val() || 0;

                let selectId = `extra_place_day${day}_index${boxIndex}`;

                let newBox = `
                    <div class="flex-1 new-place-box">
                        <label class="form-label" for="${selectId}">Place</label>
                        <select id="${selectId}" name="extra_places[${boxIndex}]"
                            class="form-select places_select2" placeholder="Select Place" style="width: 100%">
                        </select>
                    </div>
                `;

                $(this).closest('.add-place-btn').before(newBox);

                anableSelect2();
            });

            // HOTEL SECTION ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

            $(document).on('shown.bs.modal', '.edit-hotel, #addHotel', function() {
                $(this).find('#select2-hotels').select2({
                    ajax: {
                        url: '/admin/hotels/search', // Replace this with your actual endpoint
                        dataType: 'json',
                        delay: 250, // Delay in ms to throttle requests
                        data: function(params) {
                            return {
                                search: params.term, // Send the search term to the server
                                page: params.page ||
                                    1 // Implement pagination on the server side
                            };
                        },
                        processResults: function(data, params) {
                            // Parse the results into the format select2 expects
                            params.page = params.page || 1;

                            return {
                                results: data
                                    .items, // Assuming your data contains an 'items' array
                                pagination: {
                                    more: data.pagination.more // Check if there are more pages
                                }
                            };
                        },
                        cache: true
                    },
                    placeholder: 'Search for a hotel',
                    minimumInputLength: 2, // Minimum characters to type before triggering search
                    templateResult: formatOption, // Format dropdown options
                    templateSelection: formatOptionSelection, // Format selected option
                    escapeMarkup: function(markup) {
                        return markup;
                    },
                    dropdownParent: $(this)
                });
            });

            // Function to format dropdown options with title, place, and class
            function formatOption(option) {
                if (!option.id) {
                    return option.text;
                }
                var $option = $(
                    `<div>
                        <strong>${option.text}</strong><br>
                        <small class="text-muted">City: <b>${option.place}</b>, Class: <b>${option.class}</b></small>
                    </div>`
                );
                return $option;
            }

            // Function to format the selected item
            function formatOptionSelection(option) {
                if (!option.id) {
                    return option.text;
                }
                var markup =
                    `<strong>${option.text}</strong> - <small class="text-muted"><b>${option.place}</b>, Class: <b>${option.class}</b></small>`;
                return markup;
            }

            // Listen for the hotel selection and trigger an AJAX request
            $(document).on('select2:select', '.select2-hotels', function(e) {
                var hotelId = e.params.data.id; // Get the selected hotel's ID
                $this = $(this);
                $dayContainer = $(this).closest('.dstepper-block');
                let day = $dayContainer.find('.day').val();
                // Make an AJAX request with the selected hotel ID
                $.ajax({
                    url: '/admin/hotel/details/' + hotelId,
                    method: 'POST',
                    data: {
                        day: day
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr(
                            'content') // Add the CSRF token from meta tag
                    },
                    success: function(response) {
                        // Do something with the response, for example, update the DOM
                        $this.closest('.modal').find('.hotel-content').html(response);
                    },
                    error: function(error) {
                        console.error('Error fetching hotel details:', error);
                    }
                });
            });

            // Handle refresh button click
            $(document).on('click', '.refresh-hotel-content', function() {
                var $modal = $(this).closest('.modal');
                var $select = $modal.find('.select2-hotels');
                var hotelId = $select.val();

                if (!hotelId) {
                    // If no hotel is selected, just clear the content
                    $modal.find('.hotel-content').html('');
                    return;
                }

                // Find a relevant input field to use with updateCount
                var targetInput = $modal.find(
                    'input[name="single"], input[name="double"], input[name="triple"], input[name="quadruple"]'
                ).first();

                if (targetInput.length) {
                    var targetId = targetInput.attr('id');

                    // Call updateCount with the target ID
                    updateCount(targetId, false, false, function(error, response) {
                        if (error) {
                            console.error('Error updating rates:', error);
                        } else {
                            console.log("Rates updated successfully", response);
                        }
                    });
                } else {
                    console.error('No suitable input found for updateCount');
                }
            });

            // Handle click on the plus or minus buttons using document-level event delegation
            $(document).on('click', '.plus-btn', function() {
                var targetId = $(this).data('target').substring(1);
                updateCount(targetId, true, true, function(error, response) {
                    if (error) {
                        console.log(error);
                    } else {
                        console.log("Rates updated successfully", response);
                    }
                });
            });

            $(document).on('click', '.minus-btn', function() {
                var targetId = $(this).data('target').substring(1); // Remove the leading '#'
                updateCount(targetId, false, true, function(error, response) {
                    if (error) {
                        console.log(error);
                    } else {
                        console.log("Rates updated successfully", response);
                    }
                });
            });

            $(document).on('click', '.minus-btn', function() {
                var targetId = $(this).data('target').substring(1); // Remove the leading '#'
                updateCount(targetId, false, true, function(error, response) {
                    if (error) {
                        console.log(error);
                    } else {
                        console.log("Rates updated successfully", response);
                    }
                });
            });

            $(document).on('change', '[name="room_category"], [name="meal"]', function() {
                var targetId = $(this).attr('id'); // Get the ID of the changed select box

                updateCount(targetId, false, false, function(error, response) {
                    if (error) {
                        console.log(error);
                    } else {
                        console.log("Rates updated successfully", response);
                    }
                });
            });

            $(document).on('click', '#hotel-modify button[type="submit"]', function(e) {
                e.preventDefault(); // Prevent form submission
                NProgress.start();
                $dayContainer = $(this).closest('.dstepper-block');
                $hotel = $(this).closest('.hotel');
                let day = $dayContainer.find('.day').val();

                // Get all input, select, and textarea fields from the form
                var formData = {};
                $(this).closest('.hotel-inner').find('input, select, textarea').each(function() {
                    var name = $(this).attr('name');
                    if (name == "own-arrangement") {
                        var value = $(this).is(':checked');
                    } else {
                        var value = $(this).val();
                    }
                    formData[name] = value;
                });
                meal = formData['meal'];
                console.log("hotel data : ", formData);
                $.each(hotel_meals[meal], function(index, value) {
                    if (index == 0) {
                        $dayContainer.find('.meals .breakfast-group button[data-value="' + value +
                            '"]').click();
                    }
                    if (index == 1) {
                        $dayContainer.find('.meals .lunch-group button[data-value="' + value + '"]')
                            .click();
                    }
                    if (index == 2) {
                        $dayContainer.find('.meals .dinner-group button[data-value="' + value +
                                '"]')
                            .click();
                    }
                });
                // Convert formData to JSON string
                var jsonData = JSON.stringify(formData);

                // Store it in the data-hotel attribute of #hotelData div
                $hotel.find('.hotel-data').val(jsonData);
                $type = ($hotel.find('.modal').attr('id').startsWith("addHotel")) ? "add" : "modify";

                createHotelBox(jsonData, day, function(response) {
                    $hotel.find('.hotel-inner').html(response);
                    NProgress.done();
                });

                hotelIndex($dayContainer)

                if ($type == "add") {
                    if ($dayContainer.find('.hotel').length < 3) {
                        createAddHotelBox(day, function(response) {
                            $dayContainer.find('.hotels').append(response);
                            // console.log('Hotel box created successfully:', response);
                        });
                    }
                }


                $(this).closest('.modal').modal('hide');


            });

            $(document).on('click', '#hotel-modify .btn-remove', function(e) {
                e.preventDefault(); // Prevent form submission
                NProgress.start();
                $dayContainer = $(this).closest('.dstepper-block');
                $hotel = $(this).closest('.hotel');
                let day = $dayContainer.find('.day').val();

                // Store it in the data-hotel attribute of #hotelData div
                $hotel.find('.hotel-data').val("");

                createAddHotelBox(day, function(response) {
                    $hotel.replaceWith(response);
                    NProgress.done();
                });

                hotelIndex($dayContainer)
                $(this).closest('.modal').modal('hide');
            });

            // ATTRACTION SECTION ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

            $(document).on('shown.bs.modal', '.edit-attraction', function() {
                $(this).find('#select2-attractions').select2({
                    ajax: {
                        url: '/admin/attractions/search', // Adjust the endpoint to your controller
                        dataType: 'json',
                        delay: 250,
                        data: function(params) {
                            return {
                                search: params.term,
                                page: params.page || 1
                            };
                        },
                        processResults: function(data, params) {
                            params.page = params.page || 1;

                            // Group results by category (Attractions, City Tours, Excursions)
                            let groupedResults = [];

                            if (data.attractions.length > 0) {
                                groupedResults.push({
                                    text: 'Attractions',
                                    children: data.attractions
                                });
                            }
                            if (data.city_tours.length > 0) {
                                groupedResults.push({
                                    text: 'City Tours',
                                    children: data.city_tours
                                });
                            }
                            if (data.excursions.length > 0) {
                                groupedResults.push({
                                    text: 'Excursions',
                                    children: data.excursions
                                });
                            }

                            return {
                                results: groupedResults,
                                pagination: {
                                    more: data.pagination.more
                                }
                            };
                        },
                        cache: true
                    },
                    placeholder: 'Search for an attraction, city tour, or excursion',
                    minimumInputLength: 2,
                    templateResult: formatOptionAttraction, // You can format how options are shown
                    templateSelection: formatOptionSelectionAttraction, // Format the selected option
                    escapeMarkup: function(markup) {
                        return markup;
                    },
                    dropdownParent: $(this)
                });
            });

            // Handle refresh button click for attractions
            $(document).on('click', '.refresh-attraction-content', function() {
                var $modal = $(this).closest('.modal');
                var $select = $modal.find('#select2-attractions');
                var attrID = $select.val();

                if (!attrID) {
                    // If no attraction is selected, just clear the content
                    $modal.find('.attraction-content').html('');
                    return;
                }

                // Get the attraction type from the selected option
                var attrType = $select.select2('data')[0].type.split(' ').join('_').toLowerCase();

                var $dayContainer = $(this).closest('.dstepper-block');
                var day = $dayContainer.length ? $dayContainer.find('.day').val() : $modal.attr('id')
                    .replace(/^(addAttraction|editAttraction)-/, '');

                // Show loading indicator
                NProgress.start();

                // Make an AJAX request with the selected attraction ID
                $.ajax({
                    url: '/admin/attraction/details/' + attrType + "/" + attrID,
                    method: 'POST',
                    data: {
                        day: day
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        // Update the attraction content with the response
                        $modal.find('.attraction-content').html(response);
                        NProgress.done();
                    },
                    error: function(error) {
                        console.error('Error fetching attraction details:', error);
                        NProgress.done();
                    }
                });
            });

            // Function to format dropdown options with title, place, and class
            function formatOptionAttraction(option) {
                if (!option.id) {
                    return option.text;
                }
                var $option = $(
                    `<div>
                        <strong>${option.text}</strong><br>
                        <small class="text-muted">City: <b>${option.city}</b>, <span class="badge bg-label-warning me-1 ms-2">${option.type}</span></small>
                    </div>`
                );
                return $option;
            }

            // Function to format the selected item
            function formatOptionSelectionAttraction(option) {
                if (!option.id) {
                    return option.text;
                }
                var markup =
                    `<strong>${option.text}</strong> - <small class="text-muted"><b>${option.city}</b>, <span class="badge bg-label-warning me-1 ms-2">${option.type}</span></small>`;
                return markup;
            }

            // Listen for the hotel selection and trigger an AJAX request
            $(document).on('select2:select', '.select2-attractions', function(e) {
                var attrID = e.params.data.id;
                var attrType = e.params.data.type.split(' ').join('_').toLowerCase();
                $this = $(this);
                $dayContainer = $(this).closest('.dstepper-block');
                let day = $dayContainer.find('.day').val();
                // Make an AJAX request with the selected hotel ID
                $.ajax({
                    url: '/admin/attraction/details/' + attrType + "/" + attrID,
                    method: 'POST',
                    data: {
                        day: day
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr(
                            'content') // Add the CSRF token from meta tag
                    },
                    success: function(response) {
                        // Do something with the response, for example, update the DOM
                        $this.closest('.modal').find('.attraction-content').html(response);
                    },
                    error: function(error) {
                        console.error('Error fetching hotel details:', error);
                    }
                });
            });

            $(document).on('click', '#attraction-modify button[type="submit"]', function(e) {
                e.preventDefault(); // Prevent form submission
                NProgress.start();
                // Get all input, select, and textarea fields from the form
                var formData = {};
                $(this).closest('.attraction-inner').find('input, select, textarea').each(function() {
                    var name = $(this).attr('name');
                    var value = $(this).val();
                    formData[name] = value;
                });

                distance = formData['distance'];
                $dayContainer = $(this).closest('.dstepper-block');

                // Convert formData to JSON string
                var jsonData = JSON.stringify(formData);

                // Store it in the data-hotel attribute of #hotelData div
                $dayContainer = $(this).closest('.dstepper-block');
                $hotel = $(this).closest('.attraction');
                let day = $dayContainer.find('.day').val();
                $hotel.find('.attraction-data').val(jsonData);
                $type = ($hotel.find('.modal').attr('id').startsWith("addAttraction")) ? "add" : "modify";

                createAttractionBox(jsonData, day, function(response) {
                    $hotel.find('.attraction-inner').html(response);
                    NProgress.done();
                });

                attractionIndex($dayContainer)

                if ($type == "add") {
                    distance = (parseFloat(distance) || 0) + (parseFloat($dayContainer.find(
                        '.transport #distance').val()) || 0);
                    $dayContainer.find('.transport #distance').val(distance)

                    createAddAttractionBox(day, function(response) {
                        $dayContainer.find('.attractions').append(response);
                        // console.log('Hotel box created successfully:', response);
                    });
                }


                $(this).closest('.modal').modal('hide');


            });

            $(document).on('click', '#attraction-modify .btn-remove', function(e) {
                e.preventDefault(); // Prevent form submission
                NProgress.start();
                $dayContainer = $(this).closest('.dstepper-block');
                $attraction = $(this).closest('.attraction');
                let day = $dayContainer.find('.day').val();

                let jsonData = $attraction.find('.attraction-data').val();
                let decodedJson = $('<textarea/>').html(jsonData).text();

                let parsed = JSON.parse(decodedJson);
                let distance = parseFloat(parsed.distance) || 0;

                distance = (parseFloat($dayContainer.find(
                    '.transport #distance').val()) || 0) - (parseFloat(distance) || 0);
                $dayContainer.find('.transport #distance').val(distance)

                createAddAttractionBox(day, function(response) {
                    $attraction.replaceWith(response);
                    NProgress.done();
                });

                attractionIndex($dayContainer)
                $(this).closest('.modal').modal('hide');
            });
            // MEALS SECTION ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

            $(document).on('click', '.breakfast-group .btn', function() {
                // Remove active class from all buttons in the breakfast group
                $('.breakfast-group .btn').removeClass('active');
                // Add active class to the clicked button
                $(this).addClass('active');
                // Update the hidden input value
                let selectedValue = $(this).data('value');
                $('.breakfast-value').val(selectedValue);
            });

            $(document).on('click', '.lunch-group .btn', function() {
                // Remove active class from all buttons in the lunch group
                $('.lunch-group .btn').removeClass('active');
                // Add active class to the clicked button
                $(this).addClass('active');
                // Update the hidden input value
                let selectedValue = $(this).data('value');
                $('.lunch-value').val(selectedValue);
            });

            $(document).on('click', '.dinner-group .btn', function() {
                // Remove active class from all buttons in the dinner group
                $('.dinner-group .btn').removeClass('active');
                // Add active class to the clicked button
                $(this).addClass('active');
                // Update the hidden input value
                let selectedValue = $(this).data('value');
                $('.dinner-value').val(selectedValue);
            });

            // TRANSPORT SECTION ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

            $(document).on('change', '.language_id, .position_id, .type_id', function(e) {
                e.preventDefault();
                NProgress.start();
                $dayContainer = $(this).closest('.dstepper-block');
                $member = $(this).closest('.member');
                let day = $dayContainer.find('.day').val();

                // Get selected values
                var languageId = $member.find('.language_id').val();
                var positionId = $member.find('.position_id').val();
                var typeId = $member.find('.type_id').val();

                // Check if all values are selected
                if (languageId && positionId && typeId) {
                    $.ajax({
                        url: '/admin/member-prices/get-member-price', // Endpoint for retrieving price
                        type: 'GET',
                        data: {
                            language_id: languageId,
                            position_id: positionId,
                            type_id: typeId
                        },
                        success: function(response) {
                            $member.find('.member-content').html(
                                response);
                            if (!response.success) {
                                return;
                            }
                            NProgress.done();
                        },
                        error: function(xhr, status, error) {
                            console.error('Error fetching price:', error);
                        }
                    });
                }
            });

            $(document).on('click', '#member-modify button[type="submit"]', function(e) {
                e.preventDefault(); // Prevent form submission
                NProgress.start();
                $dayContainer = $(this).closest('.dstepper-block');
                $member = $(this).closest('.member');
                let day = $dayContainer.find('.day').val();

                // Get all input, select, and textarea fields from the form
                var formData = {};
                $(this).closest('.member-inner').find('input, select, textarea').each(function() {
                    var name = $(this).attr('name');
                    var value = $(this).val();
                    formData[name] = value;
                });

                // Convert formData to JSON string
                var jsonData = JSON.stringify(formData);

                // Store it in the data-hotel attribute of #hotelData div
                $member.find('.member-data').val(jsonData);
                $type = ($member.find('.modal').attr('id').startsWith("addMember")) ? "add" : "modify";

                createMemberBox(jsonData, day, function(response) {
                    $member.find('.member-inner').html(response);
                    NProgress.done();
                });

                memberIndex($dayContainer)

                if ($type == "add") {
                    if ($dayContainer.find('.member').length < 3) {
                        createAddMemberBox(day, function(response) {
                            $dayContainer.find('.members').append(response);
                            // console.log('Hotel box created successfully:', response);
                        });
                    }
                }


                $(this).closest('.modal').modal('hide');


            });

            $(document).on('click', '#member-modify .btn-remove', function(e) {
                e.preventDefault(); // Prevent form submission
                NProgress.start();
                $dayContainer = $(this).closest('.dstepper-block');
                $member = $(this).closest('.member');
                let day = $dayContainer.find('.day').val();

                // Store it in the data-hotel attribute of #hotelData div
                $member.find('.member-data').val("");

                createAddMemberBox(day, function(response) {
                    $member.replaceWith(response);
                    NProgress.done();
                });

                memberIndex($dayContainer)
                $(this).closest('.modal').modal('hide');
            });

            // ADMIN FEE SECTION ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

            $(document).on('change', '.duration_id, .category_id, .pax_id', function(e) {
                e.preventDefault();
                NProgress.start();
                $dayContainer = $(this).closest('.dstepper-block');
                $admin_fee = $(this).closest('.admin_fee');
                let day = $dayContainer.find('.day').val();

                // Get selected values
                var durationId = $admin_fee.find('.duration_id').val();
                var categoryId = $admin_fee.find('.category_id').val();
                var paxId = $admin_fee.find('.pax_id').val();

                // Check if all values are selected
                if (durationId && categoryId && paxId) {
                    $.ajax({
                        url: '/admin/admin_fee-prices/get-admin_fee-price', // Endpoint for retrieving price
                        type: 'GET',
                        data: {
                            duration_id: durationId,
                            category_id: categoryId,
                            pax_id: paxId
                        },
                        success: function(response) {
                            $admin_fee.find('.admin_fee-content').html(
                                response);
                            if (!response.success) {
                                return;
                            }
                            NProgress.done();
                        },
                        error: function(xhr, status, error) {
                            console.error('Error fetching price:', error);
                        }
                    });
                }
            });

            $(document).on('click', '#admin_fee-modify button[type="submit"]', function(e) {
                e.preventDefault(); // Prevent form submission
                NProgress.start();
                $dayContainer = $(this).closest('.dstepper-block');
                $admin_fee = $(this).closest('.admin_fee');
                let day = $dayContainer.find('.day').val();

                // Get all input, select, and textarea fields from the form
                var formData = {};
                $(this).closest('.admin_fee-inner').find('input, select, textarea').each(function() {
                    var name = $(this).attr('name');
                    var value = $(this).val();
                    formData[name] = value;
                });

                // Convert formData to JSON string
                var jsonData = JSON.stringify(formData);

                // Store it in the data-hotel attribute of #hotelData div
                $admin_fee.find('.admin_fee-data').val(jsonData);
                $type = ($admin_fee.find('.modal').attr('id').startsWith("addAdminFee")) ? "add" : "modify";

                createAdminFeeBox(jsonData, day, function(response) {
                    $admin_fee.find('.admin_fee-inner').html(response);
                    NProgress.done();
                });

                adminFeeIndex($dayContainer)

                if ($type == "add") {
                    if ($dayContainer.find('.admin_fee').length < 2) {
                        createAddAdminFeeBox(day, function(response) {
                            $dayContainer.find('.admin_fees').append(response);
                            // console.log('Hotel box created successfully:', response);
                        });
                    }
                }


                $(this).closest('.modal').modal('hide');


            });

            $(document).on('click', '#admin_fee-modify .btn-remove', function(e) {
                e.preventDefault(); // Prevent form submission
                NProgress.start();
                $dayContainer = $(this).closest('.dstepper-block');
                $admin_fee = $(this).closest('.admin_fee');
                let day = $dayContainer.find('.day').val();

                // Store it in the data-hotel attribute of #hotelData div
                $admin_fee.find('.admin_fee-data').val("");

                createAddMemberBox(day, function(response) {
                    $admin_fee.replaceWith(response);
                    NProgress.done();
                });

                adminFeeIndex($dayContainer)
                $(this).closest('.modal').modal('hide');
            });

            // OTHER SECTION ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

            $(document).on('click', '#other-modify button[type="submit"]', function(e) {
                let index = 0;
                e.preventDefault(); // Prevent form submission
                NProgress.start();
                // Get all input, select, and textarea fields from the form
                var formData = {};
                $(this).closest('.other-inner').find('input, select, textarea').each(function() {
                    var name = $(this).attr('name');
                    var value = $(this).val();
                    formData[name] = value;
                });

                // Convert formData to JSON string
                var jsonData = JSON.stringify(formData);

                // Store it in the data-hotel attribute of #hotelData div
                $dayContainer = $(this).closest('.dstepper-block');
                $other = $(this).closest('.other');
                let day = $dayContainer.find('.day').val();
                $other.find('.other-data').val(jsonData);
                $type = ($other.find('.modal').attr('id').startsWith("addOther")) ? "add" : "modify";

                index = $other.find('.card').data('index');

                createOtherBox(jsonData, day, index, function(response) {
                    $other.find('.other-inner').html(response);
                    NProgress.done();
                });

                index = otherIndex($dayContainer)

                if ($type == "add") {
                    createAddOtherBox(day, index, function(response) {
                        $dayContainer.find('.others').append(response);
                        // console.log('Hotel box created successfully:', response);
                    });
                }

                $(this).closest('.modal').modal('hide');


            });

            $(document).on('click', '#other-modify .btn-remove', function(e) {
                e.preventDefault(); // Prevent form submission
                NProgress.start();
                $dayContainer = $(this).closest('.dstepper-block');
                $other = $(this).closest('.other');
                let day = $dayContainer.find('.day').val();

                // Store it in the data-hotel attribute of #hotelData div
                $other.find('.other-data').val("");

                createAddOtherBox(day, function(response) {
                    $other.replaceWith(response);
                    NProgress.done();
                });

                otherIndex($dayContainer)
                $(this).closest('.modal').modal('hide');
            });

            // SUMMERY ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
            if (!hasRestrictedRole) {
                // Handle double-click on UL inside accordion
                $(document).on('dblclick', '.accordion-body .edit-content', function() {
                    const currentList = $(this);
                    const listHtml = currentList.html().trim(); // Extract only text
                    const voucherContainer = currentList.closest('.accordion-wrapper');

                    // Create a textarea with existing content that will be replaced with CKEditor
                    const textarea = $(
                            '<textarea id="editor-content" class="form-control edit-ul-textarea" rows="6"></textarea>'
                        )
                        .val(listHtml);
                    const saveButton = $(
                        '<button class="mt-2 btn btn-sm btn-primary save-ul-btn">Save</button>');

                    // Replace UL with textarea and show the save button
                    currentList.replaceWith(textarea);
                    voucherContainer.append(saveButton);

                    // Initialize CKEditor on the textarea
                    CKEDITOR.replace('editor-content', {
                        height: 200,
                        toolbar: [
                            ['Bold', 'Italic', 'Underline', 'Strike', 'NumberedList',
                                'BulletedList'
                            ]
                        ]
                    });
                });

                $(document).on('click', '.save-ul-btn', function() {
                    const voucherContainer = $(this).closest('.accordion-body');
                    const ulId = $(this).closest('.accordion-wrapper').data('ul-id');

                    // Get CKEditor content
                    const updatedText = CKEDITOR.instances['editor-content'].getData();
                    const wrappedContent = $('<div class="edit-content"></div>').html(updatedText);

                    // AJAX request to save updated list
                    $.ajax({
                        url: '/admin/quotation/summery/save-summery-ul', // Adjust backend route
                        type: 'POST',
                        data: {
                            _token: '{{ csrf_token() }}', // CSRF token
                            ul_id: ulId,
                            updated_list: updatedText // Saving the full HTML content
                        },
                        success: function(response) {
                            if (response.success) {
                                // Replace CKEditor with updated UL
                                CKEDITOR.instances['editor-content'].destroy();
                                $('#editor-content').replaceWith(wrappedContent);
                                $('.save-ul-btn').remove();
                                alert('Updated successfully!');
                            } else {
                                alert('Failed to update.');
                            }
                        },
                        error: function() {
                            alert('An error occurred while updating.');
                        }
                    });
                });
            }

            // Handle comment submission via AJAX
            $(document).on('click', '.addCommentForm-btn', function(e) {
                e.preventDefault(); // Prevent form submission
                NProgress.start();

                const $button = $(this);
                const originalText = $button.html();

                // Start loading state
                $button.prop('disabled', true);
                $button.html('<i class="bx bx-loader-alt bx-spin me-1"></i>Adding Comment...');

                const commentText = $('#commentText').val().trim();
                const commentId = $('#commentID').val().trim();

                if (commentText === '') {
                    alert('Please enter a comment.');
                    // Reset button state
                    $button.prop('disabled', false);
                    $button.html(originalText);
                    NProgress.done();
                    return;
                }

                $.ajax({
                    url: '{{ route('quotation.saveComment') }}',
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        comment_id: commentId,
                        comment: commentText
                    },
                    success: function(response) {
                        if (response.success) {
                            let createdAt = new Date(response.comment.created_at);
                            let formattedDate = createdAt.toLocaleString('en-US', {
                                hour: '2-digit',
                                minute: '2-digit',
                                hour12: true,
                                month: 'short',
                                day: '2-digit',
                                year: 'numeric'
                            });
                            let newComment = `
                                <li class="chat-message chat-message-right">
                                    <div class="overflow-hidden d-flex">
                                        <div class="chat-message-wrapper flex-grow-1">
                                            <div class="chat-message-text">
                                                <h6><strong>${response.comment.user_name}:</strong></h6>
                                                <p class="mb-0">${response.comment.comment}</p>
                                            </div>
                                            <div class="mt-1 text-muted">
                                                <small>${formattedDate}</small>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            `;

                            $("#commentID").val(parseInt(commentId, 10) + 1);
                            $('#commentText').val(''); // Clear input field
                            $('.chat-history-body ul').append(newComment); // Append new comment
                            NProgress.done();
                        } else {
                            alert('Failed to add comment.');
                        }

                        // Reset button state
                        $button.prop('disabled', false);
                        $button.html(originalText);
                    },
                    error: function() {
                        alert('An error occurred while adding the comment.');
                        NProgress.done();

                        // Reset button state
                        $button.prop('disabled', false);
                        $button.html(originalText);
                    }
                });
            });

            $(document).on('click', '.edit-duration', function() {
                const durationElement = $(this).closest('h5').find('.duration-text');
                const currentDuration = durationElement.text().trim();
                const saveButton = $(
                    '<button class="mt-1 btn btn-sm btn-primary save-duration-btn">Save</button>');

                // Create input field with current duration
                const inputField = $(
                        '<input type="text" class="form-control duration-input" style="width: 80px; display: inline;">'
                    )
                    .val(currentDuration);

                // Replace duration text with input field
                durationElement.replaceWith(inputField);
                $(this).after(saveButton); // Add save button next to input
                $(this).hide(); // Hide the edit button
            });

            $(document).on('click', '.save-duration-btn', function() {
                const saveButton = $(this);
                const inputField = saveButton.siblings('.duration-input');
                const newDuration = inputField.val().trim();
                const dayId = saveButton.closest('.timeline-event').data('day-id'); // Get the day ID

                if (newDuration === '') {
                    alert('Please enter a valid duration.');
                    return;
                }

                // AJAX request to save the updated duration in session
                $.ajax({
                    url: '/admin/quotation/summery/save-duration',
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}', // CSRF token
                        day_id: dayId,
                        duration: newDuration
                    },
                    success: function(response) {
                        if (response.success) {
                            // Replace input field with updated duration text
                            const durationText = $('<span class="duration-text">' +
                                newDuration + '</span>');
                            inputField.replaceWith(durationText);

                            // Restore the edit button
                            saveButton.siblings('.edit-duration').show();
                            saveButton.remove();

                            alert('Duration updated successfully!');
                        } else {
                            alert('Failed to update duration.');
                        }
                    },
                    error: function() {
                        alert('An error occurred while updating the duration.');
                    }
                });
            });
            let supplements = [];
            // Show the table when button is clicked
            $(document).on('click', '#addSupplementBtn', function() {
                $('#supplementTableWrapper').removeClass('d-none');
            });

            // Add new editable row
            $(document).on('click', '#addRowBtn', function() {
                const rowIndex = supplements.length;

                const newRow = `
                  <tr class="tr bg-label-warning" data-index="${rowIndex}">
                      <td class="pl0 editable" contenteditable="true">Title</td>
                      <td class="text-end editable" contenteditable="true">0.00</td>
                      <td class="text-center">
                          <button class="btn btn-sm btn-danger removeRowBtn">X</button>
                      </td>
                  </tr>`;

                $('#supplementTable tbody').append(newRow);
            });

            // Remove a row
            $(document).on('click', '.removeRowBtn', function() {
                const $row = $(this).closest('tr');
                const rowIndex = parseInt($row.attr('data-index'));
                // Remove from DOM
                $row.remove();

                supplements = [];
                $('#supplementTable tbody tr').each(function(index) {
                    const $row = $(this);
                    const title = $row.find('td').eq(0).text().trim();
                    const price = $row.find('td').eq(1).text().trim();

                    if (title && price) {
                        supplements.push({
                            index: index,
                            title: title,
                            amount: price
                        });

                        $row.attr('data-index', index);
                        $row.find('td.editable').removeAttr('contenteditable').addClass('saved');

                    }
                });

            });

            $(document).on('click', '#saveSupplementBtn', function() {
                supplements = [];
                $('#supplementTable tbody tr').each(function(index) {
                    const $row = $(this);
                    const title = $row.find('td').eq(0).text().trim();
                    const price = $row.find('td').eq(1).text().trim();

                    if (title && price) {
                        supplements.push({
                            index: index,
                            title: title,
                            amount: price
                        });

                        $row.attr('data-index', index);
                        $row.find('td.editable').removeAttr('contenteditable').addClass('saved');

                    }
                });

                // Send data to Laravel via AJAX
                $.ajax({
                    url: '{{ route('quotation.saveSupplements') }}',
                    method: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        updated_list: JSON.stringify(supplements)
                    },
                    success: function(response) {
                        if (response.success) {
                            alert(response.message);
                        } else {
                            alert("Something went wrong");
                        }
                    },
                    error: function() {
                        alert("AJAX error occurred");
                    }
                });
            });

            // Enable editing on double-click
            $(document).on('dblclick', 'td.saved', function() {
                $(this).attr('contenteditable', 'true').removeClass('saved').focus();
            });

            // CONFIRM

            $(document).on('click', '.dstepper-block .btn-agree', function(e, data) {
                e.preventDefault(); // Prevent form submission
                NProgress.start();

                // Get all input, select, and textarea fields from the form
                var formData = {};
                $dstepperBlock = $(this).closest('.dstepper-block');
                $dstepperBlock.find('input, select, textarea').each(function() {
                    var name = $(this).attr('name');
                    var value = $(this).val();
                    formData[name] = value;
                });

                // Convert formData to JSON string
                var jsonData = JSON.stringify(formData);

                saveQuotation(jsonData, function(response) {
                    // $("#confirm-confirm .card-body").html(response);
                    window.location.href = response.redirect;
                    NProgress.done();
                });

            });


            // HEADER

            // jQuery to handle currency selection and update
            $('.dropdown-currency .dropdown-item').on('click', function(e) {
                e.preventDefault();

                // Get the selected currency
                var currency = $(this).data('currency');

                // Update the display
                $('#currencyDisplay').text(currency);

                // Send currency selection to the server to store in session
                $.ajax({
                    url: '/admin/set-currency',
                    type: 'POST',
                    data: JSON.stringify({
                        currency: currency
                    }),
                    contentType: 'application/json',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    success: function(data) {
                        if (data.success) {
                            console.log('Currency updated successfully');

                            // load tab again

                            getDayDetails("summery", function(response) {
                                $('#summery').html(response);
                                anableSelect2();
                                $('.sub.small')
                                    .hide(); // summery page expanded
                            });

                            getDayDetails("save", function(response) {
                                $('#save').html(response);
                                anableSelect2();
                                $('.sub.small')
                                    .hide(); // summery page expanded
                            });

                            NProgress.done();
                        }
                    },
                    error: function(error) {
                        console.error('Error updating currency:', error);
                    }
                });
            });

            // jQuery to handle language selection and update
            $('.dropdown-language .dropdown-item').on('click', function(e) {
                e.preventDefault();

                // Get the selected language
                var language = $(this).data('language');

                // Update the display
                $('#languageDisplay').text(language);

                // Send language selection to the server to store in session
                $.ajax({
                    url: '/admin/set-language',
                    type: 'POST',
                    data: JSON.stringify({
                        language: language
                    }),
                    contentType: 'application/json',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    success: function(data) {
                        if (data.success) {
                            console.log('Language updated successfully');

                            // load tab again

                            getDayDetails("summery", function(response) {
                                $('#summery').html(response);
                                anableSelect2();
                                $('.sub.small')
                                    .hide(); // summery page expanded
                            });

                            getDayDetails("save", function(response) {
                                $('#save').html(response);
                                anableSelect2();
                                $('.sub.small')
                                    .hide(); // summery page expanded
                            });

                            NProgress.done();
                        }
                    },
                    error: function(error) {
                        console.error('Error updating language:', error);
                    }
                });
            });
        });

        function updateStepState(stepsState) {
            console.log(stepsState);
            stepsState.forEach(function(step) {
                var $stepElement = $('.bs-stepper-header .step[data-target="' + step.target +
                    '"] button');
                if (step.disabled) {
                    $stepElement.attr('disabled', true);
                } else {
                    $stepElement.attr('disabled', false);
                }
            });
        }

        @if (session()->has('session_expired'))
            Swal.fire({
                title: 'Session Expired',
                text: 'Your session has expired. Please create a new quotation.',
                icon: 'warning',
                confirmButtonText: 'OK'
            }).then(function() {
                window.location.href = '/admin/quotation/start'; // Redirect to create a new quotation
            });
        @endif
    </script>
@endsection
