@if ($showBreakDown)
    <div class="invoice-center {{ $classes }}">
        <div class="table-responsive">
            <table class="table mb-4 bg-white invoice-table table-sm">
                <thead class="bg-active table-dark">
                    <tr class="tr">
                        <th class="text-center text-white">No.</th>
                        <th class="text-white pl0 text-start">Description</th>
                        <th class="text-center text-white">Price</th>
                        @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                            <th class="text-center text-white">Discount</th>
                            <th class="text-center text-white">Discounted Price</th>
                        @endif
                        <th class="text-center text-white">Pax</th>
                        <th class="text-white text-end">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    @if ($rates['total']['single'])
                        <tr class="tr bg-label-warning">
                            <td>
                                <div class="text-center item-desc-1">
                                    <i class="toggle-icon bx bx-plus" style="cursor: pointer;"></i>
                                </div>
                            </td>
                            <td class="pl0">Cost Per Person Single</td>
                            <td class="text-center">{{ $rates['currency_symbol'] }}
                                {{ format_number($rates['total']['single']) }}</td>
                            @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                <td class="text-center">
                                    @if (isset($quotation['discount_type']) && $quotation['discount_type'] == 2)
                                        {{ $rates['currency_symbol'] }}
                                        {{ format_number($quotation['discount']) }}
                                    @elseif(isset($quotation['discount_type']) && $quotation['discount_type'] == 1)
                                        {{ format_number($quotation['discount']) }}%
                                    @endif
                                </td>
                                <td class="text-center">{{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['total']['single_discounted']) }}</td>
                            @endif
                            <td class="text-center">{{ $quotation['days'][1]['hotels']['single'] * 1 }}</td>
                            <td class="text-end">
                                {{ $rates['currency_symbol'] }}
                                {{ format_number($rates['total']['single_discounted'] * ($quotation['days'][1]['hotels']['single'] * 1)) }}
                            </td>
                        </tr>
                        @if ($showBreakDown)
                            <tr class="tr sub small">
                                <td>
                                    <div class="text-center item-desc-1">
                                        <span>#</span>
                                    </div>
                                </td>
                                <td class="pl0">Hotel Cost Per Person Single</td>
                                <td class="text-center">{{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['hotel']['single']) }}</td>
                                @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                    <td class="text-center"></td>
                                    <td class="text-center"></td>
                                @endif
                                <td class="text-center">{{ $quotation['days'][1]['hotels']['single'] * 1 }}</td>
                                <td class="text-end">
                                    {{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['hotel']['single'] * ($quotation['days'][1]['hotels']['single'] * 1)) }}
                                </td>
                            </tr>
                            <tr class="tr sub small">
                                <td>
                                    <div class="text-center item-desc-1">
                                        <span>#</span>
                                    </div>
                                </td>
                                <td class="pl0">Attraction Cost Per Person Single</td>
                                <td class="text-center">{{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['attraction']['single']) }}</td>
                                @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                    <td class="text-center"></td>
                                    <td class="text-center"></td>
                                @endif
                                <td class="text-center">{{ $quotation['days'][1]['hotels']['single'] * 1 }}</td>
                                <td class="text-end">
                                    {{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['attraction']['single'] * ($quotation['days'][1]['hotels']['single'] * 1)) }}
                                </td>
                            </tr>
                            <tr class="tr sub small">
                                <td>
                                    <div class="text-center item-desc-1">
                                        <span>#</span>
                                    </div>
                                </td>
                                <td class="pl0">Transport Cost Per Person Single</td>
                                <td class="text-center">{{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['transport']['single']) }}</td>
                                @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                    <td class="text-center"></td>
                                    <td class="text-center"></td>
                                @endif
                                <td class="text-center">{{ $quotation['days'][1]['hotels']['single'] * 1 }}</td>
                                <td class="text-end">
                                    {{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['transport']['single'] * ($quotation['days'][1]['hotels']['single'] * 1)) }}
                                </td>
                            </tr>
                            <tr class="tr sub small">
                                <td>
                                    <div class="text-center item-desc-1">
                                        <span>#</span>
                                    </div>
                                </td>
                                <td class="pl0">Admin Fee Per Person Single</td>
                                <td class="text-center">{{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['admin_fee']['single']) }}</td>
                                @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                    <td class="text-center"></td>
                                    <td class="text-center"></td>
                                @endif
                                <td class="text-center">{{ $quotation['days'][1]['hotels']['single'] * 1 }}</td>
                                <td class="text-end">
                                    {{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['admin_fee']['single'] * ($quotation['days'][1]['hotels']['single'] * 1)) }}
                                </td>
                            </tr>
                            <tr class="tr sub small">
                                <td>
                                    <div class="text-center item-desc-1">
                                        <span>#</span>
                                    </div>
                                </td>
                                <td class="pl0">Other Cost Per Person Single</td>
                                <td class="text-center">{{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['other']['single']) }}</td>
                                @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                    <td class="text-center"></td>
                                    <td class="text-center"></td>
                                @endif
                                <td class="text-center">{{ $quotation['days'][1]['hotels']['single'] * 1 }}</td>
                                <td class="text-end">
                                    {{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['other']['single'] * ($quotation['days'][1]['hotels']['single'] * 1)) }}
                                </td>
                            </tr>
                        @endif
                    @endif
                    @if ($rates['total']['double'])
                        <tr class="tr bg-label-warning">
                            <td>
                                <div class="text-center item-desc-1">
                                    <i class="toggle-icon bx bx-plus" style="cursor: pointer;"></i>
                                </div>
                            </td>
                            <td class="pl0">Cost Per Person Double</td>
                            <td class="text-center">{{ $rates['currency_symbol'] }}
                                {{ format_number($rates['total']['double']) }}</td>
                            @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                <td class="text-center">
                                    @if (isset($quotation['discount_type']) && $quotation['discount_type'] == 2)
                                        {{ $rates['currency_symbol'] }}
                                        {{ format_number($quotation['discount']) }}
                                    @elseif(isset($quotation['discount_type']) && $quotation['discount_type'] == 1)
                                        {{ format_number($quotation['discount']) }}%
                                    @endif
                                </td>
                                <td class="text-center">{{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['total']['double_discounted']) }}</td>
                            @endif
                            <td class="text-center">{{ $quotation['days'][1]['hotels']['double'] * 2 }}</td>
                            <td class="text-end">
                                {{ $rates['currency_symbol'] }}
                                {{ format_number($rates['total']['double_discounted'] * ($quotation['days'][1]['hotels']['double'] * 2)) }}
                            </td>
                        </tr>
                        @if ($showBreakDown)
                            <tr class="tr sub small">
                                <td>
                                    <div class="text-center item-desc-1">
                                        <span>#</span>
                                    </div>
                                </td>
                                <td class="pl0">Hotel Cost Per Person Double</td>
                                <td class="text-center">{{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['hotel']['double']) }}</td>
                                @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                    <td class="text-center"></td>
                                    <td class="text-center"></td>
                                @endif
                                <td class="text-center">{{ $quotation['days'][1]['hotels']['double'] * 2 }}</td>
                                <td class="text-end">
                                    {{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['hotel']['double'] * ($quotation['days'][1]['hotels']['double'] * 2)) }}
                                </td>
                            </tr>
                            <tr class="tr sub small">
                                <td>
                                    <div class="text-center item-desc-1">
                                        <span>#</span>
                                    </div>
                                </td>
                                <td class="pl0">Attraction Cost Per Person Double</td>
                                <td class="text-center">{{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['attraction']['double']) }}</td>
                                @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                    <td class="text-center"></td>
                                    <td class="text-center"></td>
                                @endif
                                <td class="text-center">{{ $quotation['days'][1]['hotels']['double'] * 2 }}</td>
                                <td class="text-end">
                                    {{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['attraction']['double'] * ($quotation['days'][1]['hotels']['double'] * 2)) }}
                                </td>
                            </tr>
                            <tr class="tr sub small">
                                <td>
                                    <div class="text-center item-desc-1">
                                        <span>#</span>
                                    </div>
                                </td>
                                <td class="pl0">Transport Cost Per Person Double</td>
                                <td class="text-center">{{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['transport']['double']) }}</td>
                                @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                    <td class="text-center"></td>
                                    <td class="text-center"></td>
                                @endif
                                <td class="text-center">{{ $quotation['days'][1]['hotels']['double'] * 2 }}</td>
                                <td class="text-end">
                                    {{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['transport']['double'] * ($quotation['days'][1]['hotels']['double'] * 2)) }}
                                </td>
                            </tr>
                            <tr class="tr sub small">
                                <td>
                                    <div class="text-center item-desc-1">
                                        <span>#</span>
                                    </div>
                                </td>
                                <td class="pl0">Admin Fee Per Person Double</td>
                                <td class="text-center">{{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['admin_fee']['double']) }}</td>
                                @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                    <td class="text-center"></td>
                                    <td class="text-center"></td>
                                @endif
                                <td class="text-center">{{ $quotation['days'][1]['hotels']['double'] * 2 }}</td>
                                <td class="text-end">
                                    {{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['admin_fee']['double'] * ($quotation['days'][1]['hotels']['double'] * 2)) }}
                                </td>
                            </tr>
                            <tr class="tr sub small">
                                <td>
                                    <div class="text-center item-desc-1">
                                        <span>#</span>
                                    </div>
                                </td>
                                <td class="pl0">Other Cost Per Person Double</td>
                                <td class="text-center">{{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['other']['double']) }}</td>
                                @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                    <td class="text-center"></td>
                                    <td class="text-center"></td>
                                @endif
                                <td class="text-center">{{ $quotation['days'][1]['hotels']['double'] * 2 }}</td>
                                <td class="text-end">
                                    {{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['other']['double'] * ($quotation['days'][1]['hotels']['double'] * 2)) }}
                                </td>
                            </tr>
                        @endif
                    @endif
                    @if ($rates['total']['triple'])
                        <tr class="tr bg-label-warning">
                            <td>
                                <div class="text-center item-desc-1">
                                    <i class="toggle-icon bx bx-plus" style="cursor: pointer;"></i>
                                </div>
                            </td>
                            <td class="pl0">Cost Per Person Triple</td>
                            <td class="text-center">{{ $rates['currency_symbol'] }}
                                {{ format_number($rates['total']['triple']) }}</td>
                            @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                <td class="text-center">
                                    @if (isset($quotation['discount_type']) && $quotation['discount_type'] == 2)
                                        {{ $rates['currency_symbol'] }}
                                        {{ format_number($quotation['discount']) }}
                                    @elseif(isset($quotation['discount_type']) && $quotation['discount_type'] == 1)
                                        {{ format_number($quotation['discount']) }}%
                                    @endif
                                </td>
                                <td class="text-center">{{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['total']['triple_discounted']) }}</td>
                            @endif
                            <td class="text-center">{{ $quotation['days'][1]['hotels']['triple'] * 3 }}</td>
                            <td class="text-end">
                                {{ $rates['currency_symbol'] }}
                                {{ format_number($rates['total']['triple_discounted'] * ($quotation['days'][1]['hotels']['triple'] * 3)) }}
                            </td>
                        </tr>
                        @if ($showBreakDown)
                            <tr class="tr sub small">
                                <td>
                                    <div class="text-center item-desc-1">
                                        <span>#</span>
                                    </div>
                                </td>
                                <td class="pl0">Hotel Cost Per Person Triple</td>
                                <td class="text-center">{{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['hotel']['triple']) }}</td>
                                @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                    <td class="text-center"></td>
                                    <td class="text-center"></td>
                                @endif
                                <td class="text-center">{{ $quotation['days'][1]['hotels']['triple'] * 3 }}</td>
                                <td class="text-end">
                                    {{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['hotel']['triple'] * ($quotation['days'][1]['hotels']['triple'] * 3)) }}
                                </td>
                            </tr>
                            <tr class="tr sub small">
                                <td>
                                    <div class="text-center item-desc-1">
                                        <span>#</span>
                                    </div>
                                </td>
                                <td class="pl0">Attraction Cost Per Person Triple</td>
                                <td class="text-center">{{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['attraction']['triple']) }}</td>
                                @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                    <td class="text-center"></td>
                                    <td class="text-center"></td>
                                @endif
                                <td class="text-center">{{ $quotation['days'][1]['hotels']['triple'] * 3 }}</td>
                                <td class="text-end">
                                    {{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['attraction']['triple'] * ($quotation['days'][1]['hotels']['triple'] * 3)) }}
                                </td>
                            </tr>
                            <tr class="tr sub small">
                                <td>
                                    <div class="text-center item-desc-1">
                                        <span>#</span>
                                    </div>
                                </td>
                                <td class="pl0">Transport Cost Per Person Triple</td>
                                <td class="text-center">{{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['transport']['triple']) }}</td>
                                @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                    <td class="text-center"></td>
                                    <td class="text-center"></td>
                                @endif
                                <td class="text-center">{{ $quotation['days'][1]['hotels']['triple'] * 3 }}</td>
                                <td class="text-end">
                                    {{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['transport']['triple'] * ($quotation['days'][1]['hotels']['triple'] * 3)) }}
                                </td>
                            </tr>
                            <tr class="tr sub small">
                                <td>
                                    <div class="text-center item-desc-1">
                                        <span>#</span>
                                    </div>
                                </td>
                                <td class="pl0">Admin Fee Per Person Triple</td>
                                <td class="text-center">{{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['admin_fee']['triple']) }}</td>
                                @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                    <td class="text-center"></td>
                                    <td class="text-center"></td>
                                @endif
                                <td class="text-center">{{ $quotation['days'][1]['hotels']['triple'] * 3 }}</td>
                                <td class="text-end">
                                    {{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['admin_fee']['triple'] * ($quotation['days'][1]['hotels']['triple'] * 3)) }}
                                </td>
                            </tr>
                            <tr class="tr sub small">
                                <td>
                                    <div class="text-center item-desc-1">
                                        <span>#</span>
                                    </div>
                                </td>
                                <td class="pl0">Other Cost Per Person Triple</td>
                                <td class="text-center">{{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['other']['triple']) }}</td>
                                @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                    <td class="text-center"></td>
                                    <td class="text-center"></td>
                                @endif
                                <td class="text-center">{{ $quotation['days'][1]['hotels']['triple'] * 3 }}</td>
                                <td class="text-end">
                                    {{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['other']['triple'] * ($quotation['days'][1]['hotels']['triple'] * 3)) }}
                                </td>
                            </tr>
                        @endif
                    @endif
                    @if ($rates['total']['quadruple'])
                        <tr class="tr bg-label-warning">
                            <td>
                                <div class="text-center item-desc-1">
                                    <i class="toggle-icon bx bx-plus" style="cursor: pointer;"></i>
                                </div>
                            </td>
                            <td class="pl0">Cost Per Person Quadruple</td>
                            <td class="text-center">{{ $rates['currency_symbol'] }}
                                {{ format_number($rates['total']['quadruple']) }}</td>
                            @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                <td class="text-center">
                                    @if (isset($quotation['discount_type']) && $quotation['discount_type'] == 2)
                                        {{ $rates['currency_symbol'] }}
                                        {{ format_number($quotation['discount']) }}
                                    @elseif(isset($quotation['discount_type']) && $quotation['discount_type'] == 1)
                                        {{ format_number($quotation['discount']) }}%
                                    @endif
                                </td>
                                <td class="text-center">{{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['total']['quadruple_discounted']) }}</td>
                            @endif
                            <td class="text-center">{{ $quotation['days'][1]['hotels']['quadruple'] * 4 }}</td>
                            <td class="text-end">
                                {{ $rates['currency_symbol'] }}
                                {{ format_number($rates['total']['quadruple_discounted'] * ($quotation['days'][1]['hotels']['quadruple'] * 4)) }}
                            </td>
                        </tr>
                        @if ($showBreakDown)
                            <tr class="tr sub small">
                                <td>
                                    <div class="text-center item-desc-1">
                                        <span>#</span>
                                    </div>
                                </td>
                                <td class="pl0">Hotel Cost Per Person Quadruple</td>
                                <td class="text-center">{{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['hotel']['quadruple']) }}</td>
                                @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                    <td class="text-center"></td>
                                    <td class="text-center"></td>
                                @endif
                                <td class="text-center">{{ $quotation['days'][1]['hotels']['quadruple'] * 4 }}</td>
                                <td class="text-end">
                                    {{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['hotel']['quadruple'] * ($quotation['days'][1]['hotels']['quadruple'] * 4)) }}
                                </td>
                            </tr>
                            <tr class="tr sub small">
                                <td>
                                    <div class="text-center item-desc-1">
                                        <span>#</span>
                                    </div>
                                </td>
                                <td class="pl0">Attraction Cost Per Person Quadruple</td>
                                <td class="text-center">{{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['attraction']['quadruple']) }}</td>
                                @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                    <td class="text-center"></td>
                                    <td class="text-center"></td>
                                @endif
                                <td class="text-center">{{ $quotation['days'][1]['hotels']['quadruple'] * 4 }}</td>
                                <td class="text-end">
                                    {{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['attraction']['quadruple'] * ($quotation['days'][1]['hotels']['quadruple'] * 4)) }}
                                </td>
                            </tr>
                            <tr class="tr sub small">
                                <td>
                                    <div class="text-center item-desc-1">
                                        <span>#</span>
                                    </div>
                                </td>
                                <td class="pl0">Transport Cost Per Person Quadruple</td>
                                <td class="text-center">{{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['transport']['quadruple']) }}</td>
                                @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                    <td class="text-center"></td>
                                    <td class="text-center"></td>
                                @endif
                                <td class="text-center">{{ $quotation['days'][1]['hotels']['quadruple'] * 4 }}</td>
                                <td class="text-end">
                                    {{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['transport']['quadruple'] * ($quotation['days'][1]['hotels']['quadruple'] * 4)) }}
                                </td>
                            </tr>
                            <tr class="tr sub small">
                                <td>
                                    <div class="text-center item-desc-1">
                                        <span>#</span>
                                    </div>
                                </td>
                                <td class="pl0">Admin Fee Per Person Quadruple</td>
                                <td class="text-center">{{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['admin_fee']['quadruple']) }}</td>
                                @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                    <td class="text-center"></td>
                                    <td class="text-center"></td>
                                @endif
                                <td class="text-center">{{ $quotation['days'][1]['hotels']['quadruple'] * 4 }}</td>
                                <td class="text-end">
                                    {{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['admin_fee']['quadruple'] * ($quotation['days'][1]['hotels']['quadruple'] * 4)) }}
                                </td>
                            </tr>
                            <tr class="tr sub small">
                                <td>
                                    <div class="text-center item-desc-1">
                                        <span>#</span>
                                    </div>
                                </td>
                                <td class="pl0">Other Cost Per Person Quadruple</td>
                                <td class="text-center">{{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['other']['quadruple']) }}</td>
                                @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                    <td class="text-center"></td>
                                    <td class="text-center"></td>
                                @endif
                                <td class="text-center">{{ $quotation['days'][1]['hotels']['quadruple'] * 4 }}</td>
                                <td class="text-end">
                                    {{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['other']['quadruple'] * ($quotation['days'][1]['hotels']['quadruple'] * 4)) }}
                                </td>
                            </tr>
                        @endif
                    @endif

                    @if (App\Services\QuotationService::categorizeGuests($quotation)['cwb'] > 0)
                        <tr class="tr bg-label-warning">
                            <td>
                                <div class="text-center item-desc-1">
                                    <i class="toggle-icon bx bx-plus" style="cursor: pointer;"></i>
                                </div>
                            </td>
                            <td class="pl0">Cost Per CWB</td>
                            <td class="text-center">{{ $rates['currency_symbol'] }}
                                {{ format_number($rates['total']['cwb']) }}</td>
                            @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                <td class="text-center"></td>
                                <td class="text-center"></td>
                            @endif
                            <td class="text-center">
                                {{ App\Services\QuotationService::categorizeGuests($quotation)['cwb'] }}</td>
                            <td class="text-end">
                                {{ $rates['currency_symbol'] }}
                                {{ format_number($rates['total']['cwb'] * App\Services\QuotationService::categorizeGuests($quotation)['cwb']) }}
                            </td>
                        </tr>
                        @if ($showBreakDown)
                            <tr class="tr sub small">
                                <td>
                                    <div class="text-center item-desc-1">
                                        <span>#</span>
                                    </div>
                                </td>
                                <td class="pl0">Hotel Cost Per CWB</td>
                                <td class="text-center">{{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['hotel']['cwb']) }}</td>
                                @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                    <td class="text-center"></td>
                                    <td class="text-center"></td>
                                @endif
                                <td class="text-center">
                                    {{ App\Services\QuotationService::categorizeGuests($quotation)['cwb'] }}</td>
                                <td class="text-end">
                                    {{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['hotel']['cwb'] * App\Services\QuotationService::categorizeGuests($quotation)['cwb']) }}
                                </td>
                            </tr>
                            <tr class="tr sub small">
                                <td>
                                    <div class="text-center item-desc-1">
                                        <span>#</span>
                                    </div>
                                </td>
                                <td class="pl0">Attraction Cost Per CWB</td>
                                <td class="text-center">{{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['attraction']['cwb']) }}</td>
                                @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                    <td class="text-center"></td>
                                    <td class="text-center"></td>
                                @endif
                                <td class="text-center">
                                    {{ App\Services\QuotationService::categorizeGuests($quotation)['cwb'] }}</td>
                                <td class="text-end">
                                    {{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['attraction']['cwb'] * App\Services\QuotationService::categorizeGuests($quotation)['cwb']) }}
                                </td>
                            </tr>
                        @endif
                    @endif
                    @if (App\Services\QuotationService::categorizeGuests($quotation)['cnb'] > 0)
                        <tr class="tr bg-label-warning">
                            <td>
                                <div class="text-center item-desc-1">
                                    <i class="toggle-icon bx bx-plus" style="cursor: pointer;"></i>
                                </div>
                            </td>
                            <td class="pl0">Cost Per CNB</td>
                            <td class="text-center">{{ $rates['currency_symbol'] }}
                                {{ format_number($rates['total']['cnb']) }}</td>
                            @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                <td class="text-center"></td>
                                <td class="text-center"></td>
                            @endif
                            <td class="text-center">
                                {{ App\Services\QuotationService::categorizeGuests($quotation)['cnb'] }}</td>
                            <td class="text-end">
                                {{ $rates['currency_symbol'] }}
                                {{ format_number($rates['total']['cnb'] * App\Services\QuotationService::categorizeGuests($quotation)['cnb']) }}
                            </td>
                        </tr>
                        @if ($showBreakDown)
                            <tr class="tr sub small">
                                <td>
                                    <div class="text-center item-desc-1">
                                        <span>#</span>
                                    </div>
                                </td>
                                <td class="pl0">Hotel Cost Per CWB</td>
                                <td class="text-center">{{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['hotel']['cnb']) }}</td>
                                @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                    <td class="text-center"></td>
                                    <td class="text-center"></td>
                                @endif
                                <td class="text-center">
                                    {{ App\Services\QuotationService::categorizeGuests($quotation)['cnb'] }}</td>
                                <td class="text-end">
                                    {{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['hotel']['cnb'] * App\Services\QuotationService::categorizeGuests($quotation)['cnb']) }}
                                </td>
                            </tr>
                            <tr class="tr sub small">
                                <td>
                                    <div class="text-center item-desc-1">
                                        <span>#</span>
                                    </div>
                                </td>
                                <td class="pl0">Attraction Cost Per CWB</td>
                                <td class="text-center">{{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['attraction']['cnb']) }}</td>
                                @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
                                    <td class="text-center"></td>
                                    <td class="text-center"></td>
                                @endif
                                <td class="text-center">
                                    {{ App\Services\QuotationService::categorizeGuests($quotation)['cnb'] }}</td>
                                <td class="text-end">
                                    {{ $rates['currency_symbol'] }}
                                    {{ format_number($rates['attraction']['cnb'] * App\Services\QuotationService::categorizeGuests($quotation)['cnb']) }}
                                </td>
                            </tr>
                        @endif
                    @endif
                    <!--tr class="tr2">
                        <td></td>
                        <td></td>
                        <td></td>
                        @if (isset($quotation['discount_type']) && isset($quotation['discount']) && $quotation['discount'] != 0)
<td class="text-center"></td>
                            <td class="text-center"></td>
@endif
                        <td class="text-center f-w-600 active-color fw-bold">Per Person Price</td>
                        <td class="f-w-600 text-end active-color fw-bold">{{ $rates['currency_symbol'] }}
                            {{ format_number($rates['total']['discounted_total']) }}
                        </td-->
                    </tr>

                </tbody>
            </table>
        </div>
    </div>
@else
    <div class="invoice-center {{ $classes }}">
        <div class="table-responsive">
            <table class="table mb-4 bg-white invoice-table">
                <thead class="bg-active table-dark">
                    <tr class="tr">
                        <th class="text-white pl0 text-start">{{ __('labels.description') }}</th>
                        <th class="text-white text-end">{{ __('labels.amount') }}</th>
                    </tr>
                </thead>
                <tbody>
                    @if ($rates['total']['single'])
                        <tr class="tr bg-label-warning">
                            <td class="pl0">{{ __('labels.cost_per_person_single') }}</td>
                            <td class="text-end">{{ $rates['currency_symbol'] }}
                                {{ format_number($rates['total']['single_discounted']) }}</td>
                        </tr>
                    @endif
                    @if ($rates['total']['double'])
                        <tr class="tr bg-label-warning">
                            <td class="pl0">{{ __('labels.cost_per_person_double') }}</td>
                            <td class="text-end">{{ $rates['currency_symbol'] }}
                                {{ format_number($rates['total']['double_discounted']) }}</td>
                        </tr>
                    @endif
                    @if ($rates['total']['triple'])
                        <tr class="tr bg-label-warning">
                            <td class="pl0">{{ __('labels.cost_per_person_triple') }}</td>
                            <td class="text-end">{{ $rates['currency_symbol'] }}
                                {{ format_number($rates['total']['triple_discounted']) }}</td>
                        </tr>
                    @endif
                    @if ($rates['total']['quadruple'])
                        <tr class="tr bg-label-warning">
                            <td class="pl0">{{ __('labels.cost_per_person_quadruple') }}</td>
                            <td class="text-end">{{ $rates['currency_symbol'] }}
                                {{ format_number($rates['total']['quadruple_discounted']) }}</td>
                        </tr>
                    @endif

                    @if (App\Services\QuotationService::categorizeGuests($quotation)['cwb'] > 0)
                        <tr class="tr bg-label-warning">
                            <td class="pl0">{{ __('labels.cost_per_cwb') }}</td>
                            <td class="text-end">{{ $rates['currency_symbol'] }}
                                {{ format_number($rates['total']['cwb']) }}</td>
                        </tr>
                    @endif
                    @if (App\Services\QuotationService::categorizeGuests($quotation)['cnb'] > 0)
                        <tr class="tr bg-label-warning">
                            <td class="pl0">{{ __('labels.cost_per_cnb') }}</td>
                            <td class="text-end">{{ $rates['currency_symbol'] }}
                                {{ format_number($rates['total']['cnb']) }}</td>
                        </tr>
                    @endif
                </tbody>
            </table>
        </div>
    </div>

@endif


@php
    $supplements = [];
    if (isset($quotation['summery']) && isset($quotation['summery']['supplements'])) {
        $supplementJson = $quotation['summery']['supplements'];
        $supplements = json_decode($supplementJson, true) ?? [];
    }
@endphp

@if ($showSupplement)
    @if (!$privateLink)
        @if (hasAnyRole(['Super Admin', 'Admin', 'Travel Consultant', 'Accountant']))
            <button id="addSupplementBtn" class="mb-3 btn btn-sm btn-primary">Add Supplement</button>
        @endif
        <div id="supplementTableWrapper" class="invoice-center {{ count($supplements) > 0 ? '' : 'd-none' }}">
            <div class="table-responsive">
                <table class="table mb-4 bg-white invoice-table table-sm" id="supplementTable">
                    <thead class="bg-active table-dark">
                        <tr class="tr">
                            <th class="text-white w-60 pl0 text-start"></th>
                            <th class="text-white text-end">Price</th>
                            <th class="text-center text-white">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($supplements as $supplement)
                            <tr class="tr bg-label-warning" data-index="{{ $loop->index }}">
                                <td class="pl0 editable saved" contenteditable="false">{{ $supplement['title'] }}
                                </td>
                                <td class="text-end editable saved" contenteditable="false">
                                    {{ $supplement['amount'] }}
                                </td>
                                <td class="text-center">
                                    <button class="btn btn-sm btn-danger removeRowBtn">X</button>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
                @if (hasAnyRole(['Super Admin', 'Admin', 'Travel Consultant', 'Accountant']))
                    <button id="addRowBtn" type="button" class="mb-3 btn btn-sm btn-icon btn-outline-primary">
                        <span class="icon-base bx bx-plus icon-md"></span>
                    </button>
                    <button id="saveSupplementBtn" class="btn btn-sm btn-primary float-end">Save</button>
                @endif
            </div>
        </div>

        <!-- This is where saved rows will appear (optional if you want another view of saved data) -->
        <div id="savedSupplements" class="mt-4"></div>
    @else
        <div id="supplementTableWrapper" class="invoice-center {{ count($supplements) > 0 ? '' : 'd-none' }}">
            <div class="table-responsive">
                <table class="table mb-4 bg-white invoice-table table-sm" id="supplementTable">
                    <thead class="bg-active table-dark">
                        <tr class="tr">
                            <th class="text-white w-60 pl0 text-start"></th>
                            <th class="text-white text-end">Price</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($supplements as $supplement)
                            <tr class="tr bg-label-warning" data-index="{{ $loop->index }}">
                                <td class="pl0 editable saved" contenteditable="false">{{ $supplement['title'] }}
                                </td>
                                <td class="text-end editable saved" contenteditable="false">
                                    {{ $supplement['amount'] }}
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    @endif
@endif
