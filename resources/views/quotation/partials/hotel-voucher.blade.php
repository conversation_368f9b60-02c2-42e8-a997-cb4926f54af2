<div class="card voucher-container" data-hotel-id="{{ $hotel_id }}"
    data-quotation_no="{{ $quotation['quotation_no'] }}" data-quotation_id="{{ $quotation['id'] }}"
    style="display: block; background: #f7f3dc">
    <div class="d-flex justify-content-between" id="send-voucher" data-hotel-id="{{ $hotel_id }}"
        data-quotation_no="{{ $quotation['quotation_no'] }}" data-quotation_id="{{ $quotation['id'] }}"
        style="position: absolute;top: 35px;right: 35px;cursor: pointer;">
        <span class="p-2 badge bg-label-warning rounded-2"><i class="bx bx-chevron-right bx-md text-warning"></i></span>
    </div>
    <div class="pb-4 text-center card-header" style="margin-bottom: 20px">
        <div class="" style="border: 3px solid #fb5d4336; border-radius: 10px 10px 0px 0px; padding: 20px">
            <div class="align-items-center" style="text-align: center;margin: auto;">
                <div class="mb-3 justify-content-center" style="text-align: center;margin: auto;">
                    <div class="flex-shrink-0">
                        <span class="avatar-initial avatar-shadow-primary rounded-circle"
                            style="display: block; text-align: center;margin: auto;">
                            <img src="{{ asset('assets/img/branding/en_logo.png') }}" alt="Logo" class="img-fluid"
                                style="max-width: 300px;">
                        </span>
                    </div>
                </div>
                <p class="mb-0 text-blue">Registered Office: 38/01, Stone House Suite, Nittawela Road,
                    Kandy, Sri Lanka</p>
                <p class="mb-0 text-blue">Operational Office: No 242, Kandy Road, Kurunegala, Sri Lanka</p>
                <p class="mb-0 text-blue"><EMAIL>, <EMAIL></p>
                <p class="mb-0 text-blue"> +************, +************, +94 78615615, +************</p>
                <p class="mb-0 text-blue strong">https://srilankaviajeseden.es</p>
                <p class="mb-0 small text-blue">Sri Lanka Tourism Development Authority Registration number:
                    SLTDA/SQA/TA/02238 Business Registration number: PV 106460</p>
            </div>
        </div>
    </div>
    <div class="card-body" style="margin-bottom: 20px">
        <div class="flex-wrap d-flex">
            <div class="w-20 d-flex flex-column" style="display: block; text-align: center;">
                <small class="mb-1 text-nowrap d-block">Reference No</small>
                <h6 class="mb-0">#{{ $quotation['reference_no'] }}</h6>
            </div>
            <div class="w-20 d-flex flex-column" style="display: block; text-align: center;">
                <small class="mb-1 text-nowrap d-block">Client Name/Country</small>
                <h6 class="mb-0">
                    {{ $quotation['client_name'] }} /
                    {{ optional(\App\Models\Place::find($quotation['client_country']))->name }}
                </h6>
            </div>
            <div class="w-20 d-flex flex-column" style="display: block; text-align: center;">
                <small class="mb-1 text-nowrap d-block">Created On</small>
                <h6 class="mb-0">{{ $quotation['created_at']->format('M d,Y') }}</h6>
            </div>
            <div class="w-20 d-flex flex-column" style="display: block; text-align: center;">
                <small class="mb-1 text-nowrap d-block">Created By</small>
                <h6 class="mb-0">{{ $quotation['user']->name }}</h6>
            </div>
            <div class="w-20 d-flex flex-column" style="display: block; text-align: center;">
                <small class="mb-1 text-nowrap d-block">Pax</small>
                <h6 class="mb-0">{{ $quotation['adults'] }} Adults, {{ $quotation['children'] }} Children</h6>
            </div>
        </div>
    </div>

    <div class="card-body" style="margin-bottom: 20px">
        <div class="invoice-center border-top">
            <div class="table-responsive">
                <table class="table bg-white invoice-table table-sm">
                    <thead class="bg-active table-dark">
                        <tr class="tr">
                            <th class="text-center text-white">Hotel Name</th>
                            <th class="text-white pl0 text-start">Checkin- Checkout
                            </th>
                            <th class="text-white text-start">Room Arrangement/
                                Price</th>
                            <th class="text-white text-start">Child/ Price</th>
                            <th class="text-white">Meal</th>
                            <th class="text-white">Room Category</th>
                            <th class="text-white text-end">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                    <tbody>

                        @if (isset($hotel))
                            <tr class="tr">
                                <td>
                                    <div class="text-center item-desc-1">
                                        {{ $hotel['hotel_name'] }}
                                    </div>
                                </td>
                                <td class="pl0 text-start">
                                    {{ $hotel['checkin_date'] . ' - ' . $hotel['checkout_date'] }}
                                    <br>
                                    <small>({{ $hotel['nights'] }})</small>
                                </td>
                                <td class="text-start small room-arrangement">
                                    @foreach ($hotel['room_arrangement'] as $index => $arrangement)
                                        <span class="index">{!! $arrangement !!}</span><br>
                                    @endforeach
                                </td>
                                <td class="text-start small">
                                    {!! implode('<br>', $hotel['child_rates']) !!}</td>
                                <td class="text-center">{{ $hotel['meal_type'] }}
                                </td>
                                <td class="text-center">
                                    {{ $hotel['room_category'] }}</td>
                                <td class="text-end">$
                                    {{ number_format($hotel['total_room_rate'], 2) }}
                                </td>
                                <!-- This is a placeholder amount -->
                            </tr>

                            @if (isset($hotel['discounted_rates']) && count($hotel['discounted_rates']) > 0)
                                <tr class="tr">
                                    <td colspan="2" class="text-center fw-bold">
                                        <span
                                            class="discount-title editable-text">{{ $hotel['discount_title'] ?? 'Discount given by Hotel' }}</span>
                                    </td>
                                    <td class="text-start small">
                                        @foreach ($hotel['discounted_rates'] as $type => $rate)
                                            <span class="discount-rate" data-type="{{ $type }}"
                                                data-day="{{ $hotel['day'] }}"
                                                data-original-rate="{{ $rate }}">{{ $type }} : <span
                                                    class="voucher-hotel-rate">{{ number_format($rate, 2) }}</span></span><br>
                                        @endforeach
                                    </td>
                                    <td colspan="3"></td>
                                    <td class="text-end">$
                                        {{ number_format($hotel['discount_total'] ?? 0, 2) }}
                                    </td>
                                </tr>
                            @endif
                        @endif
                        <tr class="tr2">
                            <td class="text-center f-w-600 active-color fw-bold">
                                Total
                            </td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td>
                            </td>
                            <td class="f-w-600 text-end active-color fw-bold">$
                                {{ number_format($hotel['total_rate'], 2) }}
                            </td>
                        </tr>
                    </tbody>
                    </tbody>
                </table>
            </div>
        </div>

        <p class="mt-3 card-text text-start">
        <h6>Remarks:</h6>
        <small class="text-muted remarks-text">
            @if (isset($remarks))
                <ul>
                    @foreach (explode("\n", trim($remarks)) as $line)
                        @if (!empty(trim($line)))
                            <li>{!! $line !!}</li>
                        @endif
                    @endforeach
                </ul>
            @else
                <ul>
                    <li>{{ 'Point 01' }}</li>
                    <li>{{ 'Point 02' }}</li>
                </ul>
            @endif
        </small>
        </p>
    </div>

    <div class="text-left card-footer text-start" style="margin-bottom: 20px">
        <h6>Notes:</h6>
        <small class="text-muted">
            <ul>
                <li>This voucher is valid for the confirmed booking details mentioned above.</li>
                <li>Please present this voucher upon check-in at the hotel.</li>
                <li>Standard check-in time: 2.00PM</li>
                <li>Standard check-out time: 11.00AM</li>
                <li>Early check-in and late</li>
            </ul>
        </small>
    </div>
</div>
