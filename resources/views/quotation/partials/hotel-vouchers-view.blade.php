@extends('layouts/contentNavbarLayout')

@section('title', 'View Hotel Vouchers')

@section('vendor-style')

@endsection

@section('vendor-script')


@endsection

@section('page-style')
    <link rel="stylesheet" href="{{ asset('assets/css/quotation.css') }}">
@endsection
@php
    // dd($allRate['hotel']);
@endphp
@section('content')
    <div class="row invoice-preview">
        <div class="mb-6 col-xl-12 col-md-8 col-12 mb-md-0">
            @foreach ($allRate['hotel']['data'] ?? [] as $hotel_id => $hotel)
                @include('quotation.partials.hotel-voucher', [
                    'quotation' => $quotation,
                    'hotel' => $hotel,
                    'hotel_id' => $hotel_id,
                    'remarks' => $hotelDetails[$hotel_id] ?? null,
                ])
                <hr>
            @endforeach
        </div>
    </div>
@endsection



@section('page-script')
    <script src="{{ asset('assets/js/quotation.js') }}"></script>

    <script>
        $(document).ready(function() {
            // Attach double-click event to all remarks text elements
            $(document).on('dblclick', '.remarks-text ul', function() {

                const currentList = $(this);
                const listHtml = currentList.html().replace(/<\/?li>/g, '').trim(); // Extract only text
                const voucherContainer = currentList.closest('.remarks-text');

                // Create a textarea with existing content
                const textarea = $('<textarea class="form-control remarks-input" rows="6"></textarea>')
                    .val(listHtml);
                const saveButton = $(
                    '<button class="mt-2 btn btn-sm btn-primary save-remarks-btn">Save</button>');

                // Replace UL with textarea and show the save button
                currentList.replaceWith(textarea);
                voucherContainer.append(saveButton);
            });

            // Handle save button click
            $(document).on('click', '.save-remarks-btn', function() {
                const voucherContainer = $(this).closest('.voucher-container');
                const textarea = voucherContainer.find('.remarks-input');
                const updatedText = textarea.val();
                const newList = $('<ul></ul>');

                // Convert textarea content back to a <ul> list
                updatedText.split(/\r?\n/).forEach(line => {
                    if (line.trim() !== '') {
                        newList.append('<li>' + line.trim() +
                            '</li>\n'); // Add new line after each <li>
                    }
                });


                const remarksText = voucherContainer.find('.remarks-text');
                const hotelId = voucherContainer.data('hotel-id');
                const quotationId = voucherContainer.data('quotation_id');


                // AJAX request to save remarks
                $.ajax({
                    url: '/admin/quotation/hotel-vouchers/save-remarks', // Adjust to match your backend route
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}', // CSRF token
                        hotel_id: hotelId,
                        quotation_id: quotationId,
                        remarks: updatedText
                    },
                    success: function(response) {
                        if (response.success) {
                            // Update the UI
                            textarea.replaceWith(newList);
                            $('.save-remarks-btn').remove();

                            alert('Remarks updated successfully.');
                        } else {
                            alert('Failed to update remarks.');
                        }
                    },
                    error: function() {
                        alert('An error occurred while updating the remarks.');
                    }
                });
            });


            // Add double-click handler for editing discount rates
            $(document).on("dblclick", ".voucher-hotel-rate", function() {
                var $span = $(this);
                var currentValue = $span.text().trim();
                var input =
                    `<input type="text" class="form-control edit-hotel-rate" value="${currentValue}">`;

                // Replace the span with an input field
                $span.html(input);
                $(".edit-hotel-rate").focus();
            });

            // Handle saving the edited discount rate
            $(document).on("blur keypress", ".edit-hotel-rate", function(e) {
                if (e.type === "blur" || (e.type === "keypress" && e.which === 13)) {
                    var $input = $(this);
                    var $span = $input.closest(".voucher-hotel-rate");
                    var $discountSpan = $input.closest(".discount-rate");
                    var newValue = $input.val().trim();

                    // Validate input is a number
                    if (isNaN(newValue) || newValue === "") {
                        $span.html($input.data('original-value') || "0.00");
                        return;
                    }

                    var formattedValue = parseFloat(newValue).toFixed(2);
                    var discountType = $discountSpan.data('type');
                    var voucherContainer = $span.closest('.voucher-container');
                    var quotationId = voucherContainer.data('quotation_id');
                    var hotelId = voucherContainer.data('hotel-id');
                    var day = $discountSpan.data('day');

                    // Send AJAX request to update discount rate
                    $.ajax({
                        url: "/admin/quotation/hotel-vouchers/update-hotel-rate",
                        type: "POST",
                        data: {
                            quotation_id: quotationId,
                            hotel_id: hotelId,
                            discount_type: discountType,
                            discount_rate: newValue,
                            day: day,
                            _token: $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(response) {
                            if (response) {
                                $('.voucher-container[data-hotel-id="' + hotelId +
                                    '"] .invoice-table').replaceWith(response);
                            }
                        },
                        error: function() {
                            $span.html($input.data('original-value') || "0.00");
                            alert('An error occurred while updating the discount rate');
                        }
                    });
                }
            });

            // Add double-click handler for editing the discount title
            $(document).on("dblclick", ".discount-title", function() {
                var $span = $(this);
                var currentText = $span.text().trim();
                var input =
                    `<input type="text" class="form-control edit-discount-title" value="${currentText}">`;

                // Store original text and replace with input
                $span.data('original-text', currentText);
                $span.html(input);
                $(".edit-discount-title").focus();
            });

            // Handle saving the edited discount title
            $(document).on("blur keypress", ".edit-discount-title", function(e) {
                if (e.type === "blur" || (e.type === "keypress" && e.which === 13)) {
                    var $input = $(this);
                    var $span = $input.closest(".discount-title");
                    var newText = $input.val().trim();

                    // If empty, restore original text
                    if (newText === "") {
                        $span.html($span.data('original-text'));
                        return;
                    }

                    var voucherContainer = $span.closest('.voucher-container');
                    var quotationId = voucherContainer.data('quotation_id');
                    var hotelId = voucherContainer.data('hotel-id');

                    // Send AJAX request to update discount title
                    $.ajax({
                        url: "/admin/quotation/hotel-vouchers/update-discount-title",
                        type: "POST",
                        data: {
                            quotation_id: quotationId,
                            hotel_id: hotelId,
                            discount_title: newText,
                            _token: $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(response) {
                            if (response.success) {
                                // Update the span with the new text
                                $span.html(newText);
                            } else {
                                // Restore original text on error
                                $span.html($span.data('original-text'));
                                alert('Failed to update discount title');
                            }
                        },
                        error: function() {
                            $span.html($span.data('original-text'));
                            alert('An error occurred while updating the discount title');
                        }
                    });
                }
            });
        });
    </script>
@endsection
