<form class="pt-0 add-new-attraction" id="createAttractionForm" action="{{ route('attractions.store') }}" method="POST"
    enctype="multipart/form-data">
    @csrf

    <!-- Attraction Name -->
    <div class="mb-6">
        <label class="form-label" for="name">Attraction Name</label>
        <input type="text" id="name" name="name" class="form-control" placeholder="Enter attraction name"
            value="{{ old('name') }}">
        @error('name')
            <span class="text-danger">{{ $message }}</span>
        @enderror
    </div>

    <!-- ES Name -->
    <div class="mb-6">
        <label class="form-label" for="es_name">ES Name</label>
        <input type="text" id="es_name" name="es_name" class="form-control"
            placeholder="Enter attraction name in Spanish" value="{{ old('es_name') }}">
        @error('es_name')
            <span class="text-danger">{{ $message }}</span>
        @enderror
    </div>

    <!-- FR Name -->
    <div class="mb-6">
        <label class="form-label" for="fr_name">FR Name</label>
        <input type="text" id="fr_name" name="fr_name" class="form-control"
            placeholder="Enter attraction name in French" value="{{ old('fr_name') }}">
        @error('fr_name')
            <span class="text-danger">{{ $message }}</span>
        @enderror
    </div>

    <!-- IT Name -->
    <div class="mb-6">
        <label class="form-label" for="it_name">IT Name</label>
        <input type="text" id="it_name" name="it_name" class="form-control"
            placeholder="Enter attraction name in Italian" value="{{ old('it_name') }}">
        @error('it_name')
            <span class="text-danger">{{ $message }}</span>
        @enderror
    </div>

    <!-- Description Fields with CKEditor -->
    <div class="mb-6">
        <label class="form-label" for="description">Description (English)</label>
        <textarea id="description" name="description" class="form-control ckeditor" placeholder="Enter attraction description">{{ old('description') }}</textarea>
        @error('description')
            <span class="text-danger">{{ $message }}</span>
        @enderror
    </div>

    <div class="mb-6">
        <label class="form-label" for="es_description">Description (Spanish)</label>
        <textarea id="es_description" name="es_description" class="form-control ckeditor"
            placeholder="Enter Spanish description">{{ old('es_description') }}</textarea>
        @error('es_description')
            <span class="text-danger">{{ $message }}</span>
        @enderror
    </div>

    <div class="mb-6">
        <label class="form-label" for="fr_description">Description (French)</label>
        <textarea id="fr_description" name="fr_description" class="form-control ckeditor"
            placeholder="Enter French description">{{ old('fr_description') }}</textarea>
        @error('fr_description')
            <span class="text-danger">{{ $message }}</span>
        @enderror
    </div>

    <div class="mb-6">
        <label class="form-label" for="it_description">Description (Italian)</label>
        <textarea id="it_description" name="it_description" class="form-control ckeditor"
            placeholder="Enter Italian description">{{ old('it_description') }}</textarea>
        @error('it_description')
            <span class="text-danger">{{ $message }}</span>
        @enderror
    </div>

    <!-- Place -->
    <div class="mb-6">
        <label class="form-label" for="place">Place</label>
        <select id="place" name="place" class="form-select">
            @foreach ($places as $place)
                <option value="{{ $place->id }}" {{ old('place') == $place->id ? 'selected' : '' }}>
                    {{ $place->name }}
                </option>
            @endforeach
        </select>
        @error('place')
            <span class="text-danger">{{ $message }}</span>
        @enderror
    </div>

    <!-- Categories -->
    <div class="mb-6">
        <label class="form-label" for="attraction_types">Categories</label>
        <select id="attraction_types" name="attraction_types[]" class="form-select select2" multiple>
            @foreach ($attractionTypes as $type)
                <option value="{{ $type->id }}"
                    {{ in_array($type->id, old('attraction_types', [])) ? 'selected' : '' }}>
                    {{ $type->type }}
                </option>
            @endforeach
        </select>
        @error('attraction_types')
            <span class="text-danger">{{ $message }}</span>
        @enderror
        @error('attraction_types.*')
            <span class="text-danger">{{ $message }}</span>
        @enderror
    </div>

    <!-- Duration -->
    <div class="mb-6">
        <label class="form-label" for="duration">Duration (Mnns)</label>
        <input type="number" id="duration" name="duration" class="form-control" placeholder="Enter duration"
            value="{{ old('duration') }}" required>
        @error('duration')
            <span class="text-danger">{{ $message }}</span>
        @enderror
    </div>

    <!-- Distance -->
    <div class="mb-6">
        <label class="form-label" for="distance">Distance (km)</label>
        <input type="number" id="distance" name="distance" class="form-control" placeholder="Enter distance"
            value="{{ old('distance') }}" required>
        @error('distance')
            <span class="text-danger">{{ $message }}</span>
        @enderror
    </div>

    <!-- Address -->
    <div class="mb-6">
        <label class="form-label" for="address">Address</label>
        <textarea id="address" name="address" class="form-control" placeholder="Enter attraction address">{{ old('address') }}</textarea>
        @error('address')
            <span class="text-danger">{{ $message }}</span>
        @enderror
    </div>

    <!-- Opening Time -->
    <div class="mb-6">
        <label class="form-label" for="opening">Opening Time</label>
        <input type="time" id="opening" name="opening" class="form-control" value="{{ old('opening') }}">
        @error('opening')
            <span class="text-danger">{{ $message }}</span>
        @enderror
    </div>

    <!-- Closing Time -->
    <div class="mb-6">
        <label class="form-label" for="closing">Closing Time</label>
        <input type="time" id="closing" name="closing" class="form-control" value="{{ old('closing') }}">
        @error('closing')
            <span class="text-danger">{{ $message }}</span>
        @enderror
    </div>


    <!-- Image Upload -->
    <div class="mb-3">
        <label for="image" class="form-label">Upload Image</label>
        <input type="file" id="image" name="image" class="form-control">
        @error('image')
            <span class="text-danger">{{ $message }}</span>
        @enderror
    </div>

    <!-- Select Existing Image -->
    <button type="button" class="mb-3 btn btn-outline-secondary btn-sm rounded-pill" data-bs-toggle="modal"
        data-bs-target="#imageModal">
        Select Image from Server
    </button>

    <!-- Image Selection Modal -->
    <div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Select an Image</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="imageContainer" class="flex-wrap d-flex"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Hidden Input to Store Selected Image URL -->
    <input type="hidden" id="selectedImage" name="server_image">
    <img id="previewImage" src="" style="max-width: 200px; display:none;">

    <!-- Email -->
    <div class="mb-6">
        <label class="form-label" for="email">Email</label>
        <input type="email" id="email" name="email" class="form-control" placeholder="Enter contact email"
            value="{{ old('email') }}">
        @error('email')
            <span class="text-danger">{{ $message }}</span>
        @enderror
    </div>

    <!-- Phone -->
    <div class="mb-6">
        <label class="form-label" for="phone">Phone</label>
        <input type="text" id="phone" name="phone" class="form-control" placeholder="Enter contact phone"
            value="{{ old('phone') }}">
        @error('phone')
            <span class="text-danger">{{ $message }}</span>
        @enderror
    </div>

    <!-- Submit Button -->
    <button type="submit" class="btn btn-primary me-3 data-submit">Submit</button>
    <button type="reset" class="btn btn-label-danger" data-bs-dismiss="modal">Cancel</button>
</form>
