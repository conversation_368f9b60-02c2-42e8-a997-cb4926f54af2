@extends('layouts/contentNavbarLayout')

@section('title', 'Attractions Management')

@section('vendor-style')
    <link rel="stylesheet" href="https://cdn.datatables.net/2.1.5/css/dataTables.bootstrap5.css">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
@endsection

@section('vendor-script')
    <script src="https://code.jquery.com/jquery-3.7.1.js"></script>
    <script src="https://cdn.datatables.net/2.1.5/js/dataTables.js"></script>
    <script src="https://cdn.datatables.net/2.1.5/js/dataTables.bootstrap5.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="//cdn.ckeditor.com/4.22.1/standard/ckeditor.js"></script>
@endsection

@section('page-style')
    <link rel="stylesheet" href="{{ asset('assets/css/data-table.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/css/admin.css') }}">
@endsection

@section('content')
    <div class="mb-6 row g-6">
        <div class="col-sm-6 col-xl-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-start justify-content-between">
                        <div class="content-left">
                            <span class="text-heading">Total Attractions</span>
                            <h4 class="mb-0">{{ $totalAttractions ?? 0 }}</h4>
                            <small>Total Attractions in the system</small>
                        </div>
                        <div class="avatar">
                            <span class="rounded avatar-initial bg-label-primary">
                                <i class="bx bx-landscape bx-lg"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Attractions List Table -->
    <div class="card">
        <div class="card-header border-bottom">
            <h5 class="card-title">All Attractions</h5>

            <div class="mb-3 row">
                <div class="mb-3 col-3">
                    <label for="filterPlace">Filter by Place:</label>
                    <select id="filterPlace" class="form-select">
                        <option value="">All Places</option>
                        @foreach ($places as $place)
                            <option value="{{ $place->id }}">{{ $place->name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
            @if (hasAnyRole(['Super Admin', 'Admin', 'Travel Consultant', 'Accountant']))
                <button class="btn add-new btn-primary float-end" id="addNewAttraction" type="button"
                    data-bs-toggle="offcanvas" data-bs-target="#offcanvasAddAttraction">
                    <span><i class="bx bx-plus bx-sm me-0 me-sm-2"></i>Add New Attraction</span>
                </button>
            @endif
        </div>
        <div class="card-datatable table-responsive">
            <table class="table datatables-attractions border-top">
                <thead>
                    <tr>
                        <th>Id</th>
                        <th>Name</th>
                        <th>Place</th>
                        <th>Type</th>
                        <th>Latitude</th>
                        <th>Longitude</th>
                        <th>Preferred</th>
                        <th>Actions</th>
                    </tr>
                </thead>
            </table>
        </div>

        <!-- Offcanvas to add new attraction -->
        <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasAddAttraction"
            aria-labelledby="offcanvasAddAttractionLabel">
            <div class="offcanvas-header border-bottom">
                <h5 id="offcanvasAddAttractionLabel" class="offcanvas-title">Add Attraction</h5>
                <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
            <div class="flex-grow-0 p-6 mx-0 offcanvas-body h-100">
                <!-- Add attraction form here -->
            </div>
        </div>

        <!-- Offcanvas to edit attraction -->
        <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasEditAttraction"
            aria-labelledby="offcanvasEditAttractionLabel">
            <div class="offcanvas-header border-bottom">
                <h5 id="offcanvasEditAttractionLabel" class="offcanvas-title">Edit Attraction</h5>
                <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
            <div class="flex-grow-0 p-6 mx-0 offcanvas-body h-100">
                <!-- Edit attraction form here -->
            </div>
        </div>
    </div>
@endsection

@section('page-script')
    <script>
        window.userRoles = @json(Auth::user()->roles->pluck('name')->toArray());
    </script>
    <script>
        $(document).ready(function() {
            var table = $('.datatables-attractions').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: "{{ route('attractions.getAttractions') }}",
                    data: function(d) {
                        d.place = $('#filterPlace').val();
                    }
                },
                columns: [{
                        data: 'id',
                        name: 'Id'
                    },
                    {
                        data: 'name',
                        name: 'Name'
                    },
                    {
                        data: 'place',
                        name: 'Place'
                    },
                    {
                        data: 'type',
                        name: 'Type'
                    },
                    {
                        data: 'latitude',
                        name: 'Latitude'
                    },
                    {
                        data: 'longitude',
                        name: 'Longitude'
                    },
                    {
                        data: 'prefered',
                        name: 'Preferred',
                        render: function(data) {
                            return data ? 'Yes' : 'No';
                        }
                    },
                    {
                        data: null,
                        name: 'Action',
                        orderable: false,
                        searchable: false,
                        render: function(data, type, row) {
                            let userRoles = @json(auth()->user()->getRoleNames());

                            if (userRoles.includes('Super Admin') || userRoles.includes('Admin') ||
                                userRoles.includes('Travel Consultant') || userRoles.includes(
                                    'Accountant')) {
                                return `
                                <button type="button" class="btn btn-icon delete-record text-danger me-2" data-id="${row.id}" data-name="${row.name}">
                                    <i class="bx bx-trash"></i>
                                </button>
                                <button class="btn btn-icon editAttraction text-warning" data-id="${row.id}" title="Edit" data-bs-toggle="offcanvas" data-bs-target="#offcanvasEditAttraction">
                                    <i class="bx bx-edit"></i>
                                </button>
                            `;
                            } else {
                                return "";
                            }
                        }
                    }
                ],
                pageLength: 10,
                dom: "<'row'<'col-sm-12 col-md-6 my-3'B><'col-sm-12 col-md-6 my-3'f>>" +
                    "<'row'<'col-sm-12'tr>>" +
                    "<'row'<'col-sm-12 col-md-5'l><'col-sm-12 col-md-7'p>>",
                buttons: [{
                        extend: 'copyHtml5',
                        text: '<span><i class="bx bx-copy me-2 bx-sm"></i>Copy</span>',
                        className: 'btn btn-label-secondary'
                    },
                    {
                        extend: 'excelHtml5',
                        text: '<span><i class="bx bx-file me-2 bx-sm"></i>Excel</span>',
                        className: 'btn btn-label-secondary'
                    },
                    {
                        extend: 'pdfHtml5',
                        text: '<span><i class="bx bx-file me-2 bx-sm"></i>PDF</span>',
                        className: 'btn btn-label-secondary'
                    },
                    {
                        extend: 'colvis',
                        text: '<span><i class="bx bx-columns me-2 bx-sm"></i>Column Visibility</span>',
                        className: 'btn btn-label-secondary'
                    }
                ],
                pagingType: "full_numbers",
                language: {
                    paginate: {
                        previous: '<i class="bx bx-chevron-left bx-18px"></i>',
                        next: '<i class="bx bx-chevron-right bx-18px"></i>',
                        first: '<i class="bx bx-chevrons-left bx-18px"></i>',
                        last: '<i class="bx bx-chevrons-right bx-18px"></i>'
                    },
                    search: "Find:",
                    lengthMenu: "Show _MENU_ entries",
                    info: "Displaying _START_ to _END_ of _TOTAL_ entries"
                }
            });

            $('#filterPlace').on('change', function() {
                table.ajax.reload();
            });
            // Edit button functionality
            $(document).on('click', '.editAttraction', function() {
                var attractionId = $(this).data('id');
                var url = '/admin/attractions/edit/' + attractionId;
                fetchDataWithAxios(url, {}, "GET", function(err, data) {
                    if (err) {
                        console.error('Error fetching data:', err);
                        alert('An error occurred while fetching attraction data.');
                    } else {
                        $("#offcanvasEditAttraction .offcanvas-body").html(data);
                        // Initialize CKEditor for all textareas with class 'ckeditor'
                        $('.ckeditor').each(function() {
                            initCKEditor(this.id);
                        });
                        initSelect2Filter('#attraction_types');
                    }
                });
            });

            // Add button functionality
            $(document).on('click', '#addNewAttraction', function() {
                var url = '/admin/attractions/create';
                fetchDataWithAxios(url, {}, "GET", function(err, data) {
                    if (err) {
                        console.error('Error fetching data:', err);
                        alert('An error occurred while fetching attraction data.');
                    } else {
                        $("#offcanvasAddAttraction .offcanvas-body").html(data);
                        // Initialize CKEditor for all textareas with class 'ckeditor'
                        $('.ckeditor').each(function() {
                            initCKEditor(this.id);
                        });
                        initSelect2Filter('#attraction_types');
                    }
                });
            });

            // Delete button functionality
            $(document).on('click', '.delete-record', function(e) {
                e.preventDefault();
                var attractionId = $(this).data('id');
                var attractionName = $(this).data('name');
                confirmDelete(attractionId, attractionName, function(attractionId) {
                    $.ajax({
                        url: '/admin/attractions/' + attractionId,
                        method: 'DELETE',
                        data: {
                            _token: '{{ csrf_token() }}'
                        },
                        success: function(response) {
                            Swal.fire('Deleted!', `${attractionName} has been deleted.`,
                                'success').then(() => {
                                $('.datatables-attractions').DataTable().ajax
                                    .reload(null, false);
                            });
                        },
                        error: function(error) {
                            Swal.fire('Error!',
                                'An error occurred while trying to delete the attraction.',
                                'error');
                        }
                    });
                });
            });

            // Load images when the modal is opened
            $(document).on('shown.bs.modal', '#imageModal', function() {
                fetchImages();
            });

            let currentFolder = ''; // Track current folder

            // Fetch images & folders from the server
            function fetchImages(folder = '') {
                fetch(`/admin/available-images?folder=${folder}`)
                    .then(response => response.json())
                    .then(data => {
                        let container = $("#imageContainer");
                        container.html("");

                        // Back button if inside a folder
                        if (folder !== '') {
                            let backElement = $("<div>")
                                .text("⬅ Back")
                                .addClass("folder-item m-2 p-2 border rounded text-center")
                                .css({
                                    "cursor": "pointer",
                                    "font-weight": "bold",
                                    "background": "#ddd"
                                })
                                .on("dblclick", function() {
                                    let parentFolder = folder.substring(0, folder.lastIndexOf('/'));
                                    currentFolder = parentFolder;
                                    fetchImages(parentFolder);
                                });
                            container.append(backElement);
                        }

                        // Show folders
                        data.folders.forEach(folderItem => {
                            let folderElement = $("<div>")
                                .text("📁 " + folderItem.name)
                                .addClass("folder-item m-2 p-2 border rounded text-center")
                                .css({
                                    "cursor": "pointer",
                                    "font-weight": "bold"
                                })
                                .on("dblclick", function() {
                                    currentFolder = folderItem.path;
                                    fetchImages(folderItem.path);
                                });

                            container.append(folderElement);
                        });

                        // Show images
                        data.images.forEach(image => {
                            let imgElement = $("<img>")
                                .attr("src", image.url)
                                .addClass("img-thumbnail m-2")
                                .css({
                                    "width": "100px",
                                    "cursor": "pointer"
                                })
                                .on("click", function() {
                                    $("#selectedImage").val(image.url);
                                    $("#previewImage").attr("src", image.url).show();
                                    $('#imageModal').modal('hide');
                                });

                            container.append(imgElement);
                        });
                    });
            }

            $(document).on('click', '#deleteImageBtn', function(e) {
                let attractionId = $(this).data('attraction-id');

                if (!confirm('Are you sure you want to delete this image?')) {
                    return;
                }

                $.ajax({
                    url: '{{ route('attractions.deleteImage', ':id') }}'.replace(':id',
                        attractionId),
                    type: 'DELETE',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#image-preview').remove(); // Remove the image section
                            alert(response.message);
                        } else {
                            alert(response.message);
                        }
                    },
                    error: function() {
                        alert('Failed to delete image. Please try again.');
                    }
                });
            });

            initSelect2Filter('#filterPlace');
            initSelect2Filter('#attraction_types');

            function initSelect2Filter(selector) {
                $(selector).select2();
            }
        });

        // Function to initialize CKEditor with standard configuration
        function initCKEditor(elementId) {
            CKEDITOR.replace(elementId, {
                height: 250,
                toolbar: [{
                        name: 'document',
                        items: ['Source']
                    },
                    {
                        name: 'clipboard',
                        items: ['Cut', 'Copy', 'Paste',
                            'PasteText', 'PasteFromWord',
                            '-',
                            'Undo', 'Redo'
                        ]
                    },
                    {
                        name: 'editing',
                        items: ['Find', 'Replace', '-',
                            'SelectAll'
                        ]
                    },
                    {
                        name: 'basicstyles',
                        items: ['Bold', 'Italic', 'Underline',
                            'Strike', 'Subscript',
                            'Superscript', '-',
                            'RemoveFormat'
                        ]
                    },
                    {
                        name: 'paragraph',
                        items: ['NumberedList', 'BulletedList',
                            '-', 'Outdent', 'Indent',
                            '-', 'Blockquote', '-',
                            'JustifyLeft', 'JustifyCenter',
                            'JustifyRight', 'JustifyBlock'
                        ]
                    },
                    {
                        name: 'links',
                        items: ['Link', 'Unlink']
                    },
                    {
                        name: 'insert',
                        items: ['Image', 'Table',
                            'HorizontalRule', 'SpecialChar'
                        ]
                    },
                    {
                        name: 'styles',
                        items: ['Styles', 'Format', 'Font',
                            'FontSize'
                        ]
                    },
                    {
                        name: 'colors',
                        items: ['TextColor', 'BGColor']
                    }
                ]
            });
        }
    </script>
@endsection
