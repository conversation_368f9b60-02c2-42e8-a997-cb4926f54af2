@extends('layouts/blankLayout')

@section('title', 'Login Basic - Pages')

@section('page-style')
    <!-- Page -->
    <link rel="stylesheet" href="{{ asset('assets/vendor/css/pages/page-auth.css') }}">
@endsection

@section('content')
    <div class="authentication-wrapper authentication-cover">
        <!-- Logo -->
        <a href="/" class="gap-2 auth-cover-brand d-flex align-items-center">

        </a>
        <!-- /Logo -->
        <div class="m-0 authentication-inner row">
            <!-- /Left Text -->
            <div class="p-0 m-0 d-none d-lg-flex col-lg-7 col-xl-8 align-items-center">
                <div class="d-flex justify-content-center" style="width: 100%; height: 100%">
                    <img src="{{ asset('assets/img/login-cover.jpeg') }}" class="img-fluid" alt="Login image"
                        style=" display: block;object-fit: cover; height: 620px;width: 1000px;"
                        data-app-dark-img="illustrations/boy-with-rocket-dark.png"
                        data-app-light-img="illustrations/boy-with-rocket-light.png">
                </div>
            </div>
            <!-- /Left Text -->

            <!-- Login -->
            <div class="p-6 d-flex col-12 col-lg-5 col-xl-4 align-items-center authentication-bg p-sm-12">
                <div class="mx-auto w-px-400">
                    <img src="{{ asset('assets/img/branding/logo.png') }}" alt=""
                        style="text-align: center;margin: auto;display: block;width: 230px;padding-bottom: 20px;
">

                    <h4 class="mb-1">Welcome to Viajes Eden! 👋</h4>
                    <!--p class="mb-3">Please sign-in to your account and start the adventure</p-->

                    <form id="formAuthentication" class="mb-3 fv-plugins-bootstrap5 fv-plugins-framework"
                        action="{{ route('authenticate') }}" method="POST" novalidate="novalidate">
                        @csrf
                        <div class="mb-3 fv-plugins-icon-container">
                            <label for="email" class="form-label">Email or Username</label>
                            <input type="text" class="form-control" id="email" name="email"
                                placeholder="Enter your email or username" autofocus="">
                            @error('email')
                                <div
                                    class="fv-plugins-message-container fv-plugins-message-container--enabled invalid-feedback d-block">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>
                        <div class="mb-3 form-password-toggle fv-plugins-icon-container">
                            <label class="form-label" for="password">Password</label>
                            <div class="input-group input-group-merge has-validation">
                                <input type="password" id="password" class="form-control" name="password"
                                    placeholder="············" aria-describedby="password">
                                <span class="cursor-pointer input-group-text"><i class="bx bx-hide"></i></span>
                            </div>
                            @error('password')
                                <div
                                    class="fv-plugins-message-container fv-plugins-message-container--enabled invalid-feedback d-block">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>
                        <div class="my-4">
                            <div class="d-flex justify-content-between">
                                <div class="mb-0 form-check ms-2">
                                    <input class="form-check-input" type="checkbox" id="remember-me">
                                    <label class="form-check-label" for="remember-me">
                                        Remember Me
                                    </label>
                                </div>
                                <!--a href="#">
                                        <p class="mb-0">Forgot Password?</p>
                                    </a-->
                            </div>
                        </div>
                        <button class="btn btn-primary d-grid w-100">
                            Sign in
                        </button>
                        <input type="hidden">
                    </form>

                    <!--p class="text-center">
                                <span>New on our platform?</span>
                                <a href="{{ route('register') }}">
                                    <span>Create an account</span>
                                </a>
                            </p>

                            <div class="my-6 divider">
                                <div class="divider-text">or</div>
                            </div>

                            <div class="d-flex justify-content-center">
                                <a href="javascript:;" class="btn btn-sm btn-icon rounded-circle btn-text-facebook me-1_5">
                                    <i class="bx bxl-facebook-circle"></i>
                                </a>

                                <a href="javascript:;" class="btn btn-sm btn-icon rounded-circle btn-text-twitter me-1_5">
                                    <i class="tf-icons bx bxl-twitter"></i>
                                </a>

                                <a href="javascript:;" class="btn btn-sm btn-icon rounded-circle btn-text-github me-1_5">
                                    <i class="bx bxl-github"></i>
                                </a>

                                <a href="javascript:;" class="btn btn-sm btn-icon rounded-circle btn-text-google-plus">
                                    <i class="tf-icons bx bxl-google"></i>
                                </a>
                            </div-->
                </div>
            </div>
            <!-- /Login -->
        </div>
    </div>
@endsection
