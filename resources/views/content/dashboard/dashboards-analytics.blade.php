@extends('layouts/contentNavbarLayout')

@section('title', 'Dashboard - Analytics')

@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/apex-charts/apex-charts.css') }}">
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/apex-charts/apexcharts.js') }}"></script>
@endsection

@section('page-script')
    <script src="{{ asset('assets/js/dashboards-analytics.js') }}"></script>
    <script>
        function copyPrivateLink(token) {
            const privateLink = `{{ url('quotations') }}/${token}`;

            // Use navigator.clipboard if available
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(privateLink).then(() => {
                    alert('Private link copied to clipboard');
                    window.open(privateLink, '_blank'); // Opens in a new tab
                }).catch(err => {
                    console.error('Failed to copy private link:', err);
                });
            } else {
                // Fallback method for unsupported browsers
                const textArea = document.createElement("textarea");
                textArea.value = privateLink;
                textArea.style.position = "fixed"; // Prevents scrolling to the bottom
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();

                try {
                    const successful = document.execCommand('copy');
                    alert(successful ? 'Private link copied to clipboard' : 'Failed to copy private link');
                    window.open(privateLink, '_blank'); // Opens in a new tab
                } catch (err) {
                    console.error('Fallback: Failed to copy private link', err);
                }

                document.body.removeChild(textArea);
            }
        }
    </script>
@endsection

@section('content')
    <div class="row">
        <div class="mb-4 col-lg-8 order-0">
            <div class="card">
                <div class="d-flex align-items-end row">
                    <div class="col-sm-7">
                        <div class="card-body">
                            <h5 class="card-title text-primary">Congratulations {{ Auth::user()->name }}! 🎉</h5>
                            <p class="mb-4">
                                You are logged in as <span
                                    class="fw-medium">{{ Auth::user()->getRoleNames()->first() ?? 'User' }}</span>.
                            </p>
                        </div>
                    </div>
                    <div class="text-center col-sm-5 text-sm-left">
                        <div class="px-0 pb-0 card-body px-md-4">
                            <img src="{{ asset('assets/img/illustrations/man-with-laptop-light.png') }}" height="140"
                                alt="View Badge User" data-app-dark-img="illustrations/man-with-laptop-dark.png"
                                data-app-light-img="illustrations/man-with-laptop-light.png">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if (Auth::user()->hasRole('Customer'))
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">Your Quotations</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th>Reference No.</th>
                                        <th>Date</th>
                                        <th>Total Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @php
                                        // Get all quotations for the current customer where disabled==0
                                        // Show each version as a separate row
                                        // Consider soft-deleted records as well
                                        $customerQuotations = \App\Models\Quotation::withTrashed()
                                            ->where('client_name', Auth::id())
                                            ->where('disabled', 0)
                                            ->orderBy('quotation_no', 'desc')
                                            ->orderBy('id', 'desc')
                                            ->get();

                                        // Group quotations by quotation_no to calculate version numbers
                                        $quotationGroups = $customerQuotations->groupBy('quotation_no');
                                    @endphp

                                    @forelse($customerQuotations as $quotation)
                                        @php
                                            // Calculate version number for this quotation
                                            $versionsInGroup = $quotationGroups[$quotation->quotation_no];
                                            $totalVersions = $versionsInGroup->count();
                                            $versionIndex = $versionsInGroup->search(function ($item) use ($quotation) {
                                                return $item->id === $quotation->id;
                                            });
                                            $versionNumber = $totalVersions - $versionIndex;
                                        @endphp
                                        <tr data-quotation-no="{{ $quotation->quotation_no }}"
                                            data-version-id="{{ $quotation->id }}">
                                            <td><a href="{{ url('quotations/' . $quotation->token) }}"
                                                    target="_blank">{{ $quotation->reference_no }}</a>
                                            </td>
                                            <td>{{ $quotation->created_at->format('Y-m-d') }}</td>
                                            <td>${{ number_format($quotation->total, 2) }}</td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="6" class="text-center">No quotations found</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

@endsection
