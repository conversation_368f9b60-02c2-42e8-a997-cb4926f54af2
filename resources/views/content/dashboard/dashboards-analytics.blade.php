@extends('layouts/contentNavbarLayout')

@section('title', 'Dashboard - Analytics')

@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/apex-charts/apex-charts.css') }}">
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/apex-charts/apexcharts.js') }}"></script>
@endsection

@section('page-script')
    <script src="{{ asset('assets/js/dashboards-analytics.js') }}"></script>
    <script>
        function copyPrivateLink(token) {
            const privateLink = `{{ url('quotations') }}/${token}`;

            // Use navigator.clipboard if available
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(privateLink).then(() => {
                    alert('Private link copied to clipboard');
                    window.open(privateLink, '_blank'); // Opens in a new tab
                }).catch(err => {
                    console.error('Failed to copy private link:', err);
                });
            } else {
                // Fallback method for unsupported browsers
                const textArea = document.createElement("textarea");
                textArea.value = privateLink;
                textArea.style.position = "fixed"; // Prevents scrolling to the bottom
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();

                try {
                    const successful = document.execCommand('copy');
                    alert(successful ? 'Private link copied to clipboard' : 'Failed to copy private link');
                    window.open(privateLink, '_blank'); // Opens in a new tab
                } catch (err) {
                    console.error('Fallback: Failed to copy private link', err);
                }

                document.body.removeChild(textArea);
            }
        }
    </script>
@endsection

@section('content')
    <div class="row">
        <div class="mb-4 col-lg-8 order-0">
            <div class="card">
                <div class="d-flex align-items-end row">
                    <div class="col-sm-7">
                        <div class="card-body">
                            <h5 class="card-title text-primary">Congratulations {{ Auth::user()->name }}! 🎉</h5>
                            <p class="mb-4">
                                You are logged in as <span
                                    class="fw-medium">{{ Auth::user()->getRoleNames()->first() ?? 'User' }}</span>.
                            </p>
                        </div>
                    </div>
                    <div class="text-center col-sm-5 text-sm-left">
                        <div class="px-0 pb-0 card-body px-md-4">
                            <img src="{{ asset('assets/img/illustrations/man-with-laptop-light.png') }}" height="140"
                                alt="View Badge User" data-app-dark-img="illustrations/man-with-laptop-dark.png"
                                data-app-light-img="illustrations/man-with-laptop-light.png">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if (Auth::user()->hasRole('Customer'))
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">Your Quotations</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th>Quotation No.</th>
                                        <th>Reference No.</th>
                                        <th>Date</th>
                                        <th>Total Amount</th>
                                        <th>Version</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @php
                                        // Get quotations for the current customer where disabled==0
                                        // Group by quotation_no to get unique quotations, then get latest enabled version of each
                                        $customerQuotations = \App\Models\Quotation::where('client_name', Auth::id())
                                            // ->where('disabled', 0)
                                            ->select('quotation_no', \DB::raw('MAX(id) as latest_id'))
                                            ->groupBy('quotation_no')
                                            ->get()
                                            ->map(function ($item) {
                                                return \App\Models\Quotation::find($item->latest_id);
                                            })
                                            ->filter()
                                            ->sortByDesc('created_at');
                                    @endphp

                                    @forelse($customerQuotations as $quotation)
                                        @php
                                            // Get all enabled versions of this quotation
                                            $enabledVersions = \App\Models\Quotation::where(
                                                'quotation_no',
                                                $quotation->quotation_no,
                                            )
                                                ->where('disabled', 0)
                                                ->orderByDesc('id')
                                                ->get();
                                            $totalVersions = $enabledVersions->count();
                                        @endphp
                                        <tr data-quotation-no="{{ $quotation->quotation_no }}">
                                            <td>{{ $quotation->quotation_no }}</td>
                                            <td>{{ $quotation->reference_no }}</td>
                                            <td>{{ $quotation->created_at->format('Y-m-d') }}</td>
                                            <td class="quotation-total">${{ number_format($quotation->total, 2) }}</td>
                                            <td>
                                                @if ($totalVersions > 1)
                                                    <select class="form-select form-select-sm version-select-customer"
                                                        data-quotation-no="{{ $quotation->quotation_no }}"
                                                        style="min-width: 120px;">
                                                        @foreach ($enabledVersions as $index => $version)
                                                            <option value="{{ $version->id }}"
                                                                data-token="{{ $version->token }}"
                                                                data-total="{{ $version->total }}"
                                                                {{ $version->id == $quotation->id ? 'selected' : '' }}>
                                                                Version
                                                                {{ str_pad($totalVersions - $index, 2, '0', STR_PAD_LEFT) }}
                                                                <span class="text-success">●</span>
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                @else
                                                    <span class="badge bg-success">
                                                        <span class="text-white">●</span> Version 01
                                                    </span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="dropdown">
                                                    <button type="button" class="btn p-0 dropdown-toggle hide-arrow"
                                                        data-bs-toggle="dropdown">
                                                        <i class="bx bx-dots-vertical-rounded"></i>
                                                    </button>
                                                    <div class="dropdown-menu">
                                                        <a class="dropdown-item quotation-link"
                                                            href="{{ url('quotations/' . $quotation->token) }}"
                                                            target="_blank">
                                                            <i class="bx bx-link-alt me-1"></i> View Quotation
                                                        </a>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="6" class="text-center">No quotations found</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

@endsection

@section('page-script')
    <script>
        $(document).ready(function() {
            // Handle version selection change
            $('.version-select-customer').on('change', function() {
                const selectedOption = $(this).find('option:selected');
                const newToken = selectedOption.data('token');
                const newTotal = selectedOption.data('total');
                const row = $(this).closest('tr');

                // Update the total amount in the same row
                row.find('.quotation-total').text('$' + new Intl.NumberFormat().format(newTotal));

                // Update the quotation link in the actions dropdown
                const newUrl = '{{ url('quotations') }}/' + newToken;
                row.find('.quotation-link').attr('href', newUrl);

                // Optional: Show a brief success message
                const versionText = selectedOption.text().trim();

                // You can add a toast notification here if needed
                // toastr.success('Switched to ' + versionText);
            });
        });
    </script>
@endsection
