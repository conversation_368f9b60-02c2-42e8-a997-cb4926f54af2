'use strict';

(function() {

})();


function createHotelBox(hotelData, day, callback) {
    // Perform an AJAX request to get hotel details
    $.ajax({
        url: '/admin/hotel/create-box', // Replace with the correct API endpoint
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ hotel: hotelData, day: day }),
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') // Add the CSRF token from meta tag
        },
        success: function(response) {
            if (typeof callback === 'function') {
                callback(response);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error creating hotel box:', error);
        }
    });
}

function createAddHotelBox(day, callback) {
    // Perform an AJAX request to get hotel details
    $.ajax({
        url: '/admin/hotel/create-add-box', // Replace with the correct API endpoint
        type: 'GET',
        contentType: 'application/json',
        data: { day: day },
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') // Add the CSRF token from meta tag
        },
        success: function(response) {
            if (typeof callback === 'function') {
                callback(response);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error creating hotel box:', error);
        }
    });
}

function saveSession(data, callback) {
    // Perform an AJAX request to get hotel details
    $.ajax({
        url: '/admin/quotation/session-save', // Replace with the correct API endpoint
        type: 'POST',
        contentType: 'application/json',
        data: data,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') // Add the CSRF token from meta tag
        },
        success: function(response) {
            if (typeof callback === 'function') {
                callback(response, 'success');
            }
        },
        error: function(xhr, status, error) {
            // Call the callback function with an error
            if (typeof callback === 'function') {
                callback(error, status);
            }
        }
    });
}

function hotelIndex($obj) {
    $($obj).find('.hotels .hotel').each(function(index) {
        $(this).find('.hotel-data').attr('name', "hotel[" + index + "]"); // Assign a data-index attribute starting from 1
    });
}

function updateCount(inputId, isIncrement, isIncItem, callback) {
    var inputElement = $('#' + inputId);

    if(isIncItem) {
        var currentValue = parseInt(inputElement.val());

        // Update the value based on whether it is incrementing or decrementing
        if (isIncrement) {
            inputElement.val(currentValue + 1);
        } else if (currentValue > 0) {
            inputElement.val(currentValue - 1);
        }
    }

    var hotelContent = inputElement.closest('#hotel-modify');
    var dayContent = inputElement.closest('.day-details');


    let bedCounts = {
        1: hotelContent.find('input[name="single"]').val() || 0,
        2: hotelContent.find('input[name="double"]').val() || 0,
        3: hotelContent.find('input[name="triple"]').val() || 0,
        4: hotelContent.find('input[name="quadruple"]').val() || 0,
    };
    let roomCategory = hotelContent.find('[name="room_category"]').val() || 0
    let meal = hotelContent.find('[name="meal"]').val() || 0
    let id = hotelContent.find("#select2-hotels").val() || 0
    let day = dayContent.find('input[name="day"]').val() || 0

    // Perform the AJAX request to get the updated hotel rates
    $.ajax({
        url: '/admin/hotel/rates/' + id, // Replace this with your actual endpoint
        type: 'POST',
        dataType: 'html', // Expecting an HTML response
        data: {
            bedCounts: bedCounts,
            roomCategory: roomCategory,
            meal: meal,
            day: day,
            _token: $('meta[name="csrf-token"]').attr('content') // Include CSRF token
        },
        success: function(response) {
            console.log(hotelContent.find('.hotel-rates-section'));
            console.log(response);
            // Replace the content of the hotel rates section with the HTML response
            hotelContent.find('.hotel-rates-section').html(response);

            // Call the callback function and pass the response data
            if (typeof callback === 'function') {
                callback(null, response); // Pass response data if successful
            }
        },
        error: function(xhr, status, error) {
            console.error('Error:', error);

            // Call the callback function with an error
            if (typeof callback === 'function') {
                callback(error, null);
            }
        }
    });
}

function attractionIndex($obj) {
    $($obj).find('.attractions .attraction').each(function(index) {
        $(this).find('.attraction-data').attr('name', "attraction[" + index + "]"); // Assign a data-index attribute starting from 1
    });
}

function createAttractionBox(attrData, day, callback) {
    // Perform an AJAX request to get hotel details
    $.ajax({
        url: '/admin/attraction/create-box', // Replace with the correct API endpoint
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ attraction: attrData, day: day }),
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') // Add the CSRF token from meta tag
        },
        success: function(response) {
            if (typeof callback === 'function') {
                callback(response);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error creating attraction box:', error);
        }
    });
}

function createAddAttractionBox(day, callback) {
    // Perform an AJAX request to get hotel details
    $.ajax({
        url: '/admin/attraction/create-add-box', // Replace with the correct API endpoint
        type: 'GET',
        contentType: 'application/json',
        data: { day: day },
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') // Add the CSRF token from meta tag
        },
        success: function(response) {
            if (typeof callback === 'function') {
                callback(response);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error creating hotel box:', error);
        }
    });
}

function getDayDetails(day, callback) {
    // Perform an AJAX request to get hotel details
    $.ajax({
        url: '/admin/quotation/day', // Replace with the correct API endpoint
        type: 'GET',
        contentType: 'application/json',
        data: { day: day },
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') // Add the CSRF token from meta tag
        },
        success: function(response) {
            if (typeof callback === 'function') {
                callback(response);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error creating hotel box:', error);
        }
    });
}

function calculatePaxAndSendRequest(countryID, arrivalDate, callback) {
    // Get the number of adults
    let adults = parseInt($('#adults').val()) || 0;

    // Initialize children count with age > 2
    let childrenAboveTwo = 0;

    // Loop through all the children age input fields
    $('input[name="children_ages[]"]').each(function() {
        let age = parseInt($(this).val()) || 0;
        if (age > 2) {
            childrenAboveTwo++; // Count children only if age > 2
        }
    });

    // Calculate totalPax
    let totalPax = adults + childrenAboveTwo;

    // Send AJAX request with total pax
    $.ajax({
        url: '/admin/quotation/get-vehicle-rate', // Update with your correct URL for QuotationController
        type: 'GET',
        data: {
            pax: totalPax,
            countryID: countryID,
            arrivalDate: arrivalDate,
            _token: $('meta[name="csrf-token"]').attr('content') // CSRF token
        },
        success: function(response) {
            // Handle the response from the server
            if (response.success) {
                if (typeof callback === 'function') {
                    callback(response);
                }
            } else {
                console.error('No matching vehicles found.');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error fetching vehicle rates:', error);
        }
    });
}

function getTotalDistance(placeIds, callback) {
  $.ajax({
      url: '/admin/quotation/calculate-distance',
      type: 'POST',
      data: {
          places: placeIds,
          _token: $('meta[name="csrf-token"]').attr('content')
      },
      success: function (response) {
          if (response.distance) {
              callback(null, response.distance);
          } else {
              callback('Error calculating distance', null);
          }
      },
      error: function (xhr, status, error) {
          callback('AJAX error: ' + error, null);
      }
  });
}

function saveQuotation(data, callback) {
    // Perform an AJAX request to get hotel details
    $.ajax({
        url: '/admin/quotation/save', // Replace with the correct API endpoint
        type: 'POST',
        contentType: 'application/json',
        data: data,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') // Add the CSRF token from meta tag
        },
        success: function(response) {
            if (typeof callback === 'function') {
                callback(response);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error creating hotel box:', error);
        }
    });
}

function sendHotelVouchers(hotel_id, quotation_no, quotation_id, callback) {
    // Perform an AJAX request to get hotel details
    $.ajax({
        url: '/admin/quotation/send/hotel-vouchers', // Replace with the correct API endpoint
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ hotel_id,quotation_no, quotation_id }),
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') // Add the CSRF token from meta tag
        },
        success: function(response) {
            if (response.success) {
                Swal.fire({
                    title: "Success!",
                    text: "The hotel voucher has been sent to " + response.email,
                    icon: "success",
                    confirmButtonText: "OK"
                });
            } else {
                Swal.fire({
                    title: "Error!",
                    text: response.message || "Failed to send the hotel voucher.",
                    icon: "error",
                    confirmButtonText: "OK"
                });
            }

            if (typeof callback === 'function') {
                callback(response);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error sending hotel vouchers:', error);
            Swal.fire({
              title: "Error!",
              text: "An error occurred while sending the hotel voucher.",
              icon: "error",
              confirmButtonText: "OK"
          });
        }
    });
}

function createMemberBox(memberData, day, callback) {
    // Perform an AJAX request to get hotel details
    $.ajax({
        url: '/admin/member/create-box', // Replace with the correct API endpoint
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ member: memberData, day: day }),
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') // Add the CSRF token from meta tag
        },
        success: function(response) {
            if (typeof callback === 'function') {
                callback(response);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error creating member box:', error);
        }
    });
}

function memberIndex($obj) {
    $($obj).find('.members .member').each(function(index) {
        $(this).find('.member-data').attr('name', "member[" + index + "]"); // Assign a data-index attribute starting from 1
    });
}

function createAddMemberBox(day, callback) {
    // Perform an AJAX request to get member details
    $.ajax({
        url: '/admin/member/create-add-box', // Replace with the correct API endpoint
        type: 'GET',
        contentType: 'application/json',
        data: { day: day },
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') // Add the CSRF token from meta tag
        },
        success: function(response) {
            if (typeof callback === 'function') {
                callback(response);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error creating member box:', error);
        }
    });
}

function createAdminFeeBox(adminFeeData, day, callback) {
  // Perform an AJAX request to get hotel details
  $.ajax({
      url: '/admin/admin_fee/create-box', // Replace with the correct API endpoint
      type: 'POST',
      contentType: 'application/json',
      data: JSON.stringify({ admin_fee: adminFeeData, day: day }),
      headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') // Add the CSRF token from meta tag
      },
      success: function(response) {
          if (typeof callback === 'function') {
              callback(response);
          }
      },
      error: function(xhr, status, error) {
          console.error('Error creating admin_fee box:', error);
      }
  });
}

function adminFeeIndex($obj) {
  $($obj).find('.admin_fees .admin_fee').each(function(index) {
      $(this).find('.admin_fee-data').attr('name', "admin_fee[" + index + "]"); // Assign a data-index attribute starting from 1
  });
}

function createAddAdminFeeBox(day, callback) {
  // Perform an AJAX request to get admin_fee details
  $.ajax({
      url: '/admin/admin_fee/create-add-box', // Replace with the correct API endpoint
      type: 'GET',
      contentType: 'application/json',
      data: { day: day },
      headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') // Add the CSRF token from meta tag
      },
      success: function(response) {
          if (typeof callback === 'function') {
              callback(response);
          }
      },
      error: function(xhr, status, error) {
          console.error('Error creating admin_fee box:', error);
      }
  });
}


$(document).ready(function() {
    $(document).on('click', '#send-voucher', function(e) {
        NProgress.start();
        let hotel_id = $(this).data('hotel-id');
        let quotation_no = $(this).data('quotation_no');
        let quotation_id = $(this).data('quotation_id');

        sendHotelVouchers(hotel_id, quotation_no, quotation_id,  function(response) {
            NProgress.done();
        });
    });

    $(document).on('click', '.tr.bg-label-warning', function() {
        $(this).nextUntil('.bg-label-warning').filter('.sub.small').toggle();

        var toggleIcon = $(this).find('.toggle-icon');
        if ($(this).next('.sub.small').is(':visible')) {
            toggleIcon.removeClass('bx-plus').addClass('bx-minus');
        } else {
            toggleIcon.removeClass('bx-minus').addClass('bx-plus');
        }
    });
});

// Others

function otherIndex($obj) {
  let i = 0;
  $($obj).find('.others .other').each(function(index) {
      $(this).find('.other-data').attr('name', "other[" + index + "]"); // Assign a data-index attribute starting from 1
      i++;
  });

  return i;
}

function createOtherBox(otherData, day, index, callback) {
  // Perform an AJAX request to get hotel details
  $.ajax({
      url: '/admin/other/create-box', // Replace with the correct API endpoint
      type: 'POST',
      contentType: 'application/json',
      data: JSON.stringify({ other: otherData, day: day , index: index}),
      headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') // Add the CSRF token from meta tag
      },
      success: function(response) {
          if (typeof callback === 'function') {
              callback(response);
          }
      },
      error: function(xhr, status, error) {
          console.error('Error creating other box:', error);
      }
  });
}

function createAddOtherBox(day, index, callback) {
  // Perform an AJAX request to get other details
  $.ajax({
      url: '/admin/other/create-add-box', // Replace with the correct API endpoint
      type: 'GET',
      contentType: 'application/json',
      data: { day: day, index: index },
      headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') // Add the CSRF token from meta tag
      },
      success: function(response) {
          if (typeof callback === 'function') {
              callback(response);
          }
      },
      error: function(xhr, status, error) {
          console.error('Error creating other box:', error);
      }
  });
}
