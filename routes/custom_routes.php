<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\dashboard\Analytics;
use App\Http\Controllers\Auth\LoginRegisterController;
use App\Http\Controllers\User\ProfileController;
use App\Http\Controllers\User\RoleController;
use App\Http\Controllers\User\CustomerController;
use App\Http\Controllers\User\PermissionController;
use App\Http\Controllers\Place\PlaceController;
use App\Http\Controllers\Company\CompanyController;
use App\Http\Controllers\Quotation\QuotationController;
use App\Http\Controllers\Quotation\QuotationManageController;
use App\Http\Controllers\Transport\TransportController;
use App\Http\Controllers\Common\ServiceController;
use App\Http\Controllers\Attraction\AttractionController;
use App\Http\Controllers\Hotel\HotelController;
use App\Http\Controllers\Quotation\TemplateController;
use App\Http\Controllers\Other\OtherController;

use App\Http\Controllers\Hotel\HotelAdminController;
use App\Http\Controllers\Hotel\HotelMealAdminController;
use App\Http\Controllers\Hotel\HotelCategoryAdminController;
use App\Http\Controllers\Hotel\HotelRoomRateAdminController;
use App\Http\Controllers\Attraction\AttractionAdminController;
use App\Http\Controllers\Attraction\CityTourAdminController;
use App\Http\Controllers\Attraction\ExcursionAdminController;
use App\Http\Controllers\Attraction\AttractionRateAdminController;
use App\Http\Controllers\Attraction\CityTourRateAdminController;
use App\Http\Controllers\Attraction\ExcursionRateAdminController;
use App\Http\Controllers\Vehicle\VehicleTypeAdminController;
use App\Http\Controllers\Vehicle\VehicleRateAdminController;
use App\Http\Controllers\Operation\MemberController;
use App\Http\Controllers\Operation\MemberPriceController;
use App\Http\Controllers\Operation\MemberAllocateController;
use App\Http\Controllers\Quotation\QuotationCommentController;
use App\Http\Controllers\Quotation\QuotationDocumentsController;


use App\Http\Controllers\SettingsController;
use App\Http\Controllers\Report\ReportController;

use App\Http\Controllers\Package\PackageController;
use App\Http\Controllers\Booking\BookingController;
use App\Http\Controllers\Job\RunningJobController;

use App\Http\Controllers\AdminFee\AdminFeePriceController;
use App\Http\Controllers\Admin\WhatsAppController;


use Illuminate\Support\Facades\Artisan;

use App\Http\Controllers\ImageController;
use Illuminate\Support\Facades\File;
use Illuminate\Http\Request;

Route::get('/artisan/{command}', function ($command) {
  // Check if the command is allowed for execution
  $allowedCommands = [
    'queue:listen',
    'queue:work',
    'storage:link',
    'key:generate',
    'cache:clear',
    'config:clear',
    'route:clear',
    'view:clear',
    'migrate',
    'optimize',
    'optimize:clear',
    // Add other commands you want to allow
  ];

  if (in_array($command, $allowedCommands)) {
    if ($command === 'storage:link') {
      // Create symlink manually
      $source = storage_path('app/public');
      $destination = public_path('storage');
      if (!file_exists($destination)) {
        symlink($source, $destination);
        return response()->json([
          'status' => 'success',
          'message' => "Symlink created successfully.",
        ]);
      }
      return response()->json([
        'status' => 'success',
        'message' => "Symlink already exists.",
      ]);
    }

    $exitCode = Artisan::call($command);
    return response()->json([
      'status' => 'success',
      'message' => "Artisan command '$command' executed successfully.",
      'output' => Artisan::output()
    ]);
  }

  // If the command is not allowed
  return response()->json([
    'status' => 'error',
    'message' => "Command '$command' is not allowed."
  ], 403);
});

Route::get('/queue-work', function () {
  exec('php artisan queue:work --tries=3 > /dev/null 2>&1 &');
  return '<h1>Queue Work Started</h1>';
});

Route::controller(LoginRegisterController::class)->group(function () {
  Route::get('/register', 'register')->name('register');
  Route::post('/store', 'store')->name('store');
  Route::get('/login', 'login')->name('login');
  Route::post('/authenticate', 'authenticate')->name('authenticate');
  Route::get('/logout', 'logout')->name('logout');
});

Route::get('/quotations/{token}', [QuotationController::class, 'showByToken'])
    ->middleware(['auth'])
    ->name('quotations.show');
Route::get('/quotations-pdf/{token}', [QuotationController::class, 'showByTokenPdf'])->name('quotations.showPdf');
Route::post('quotation/save-comment', [QuotationController::class, 'saveCommentToDatabase'])->name('quotation.saveCommentToDatabase');


Route::get('/packages', [PackageController::class, 'index'])->name('packages.index');
Route::get('/packages/filter', [PackageController::class, 'filter'])->name('packages.filter');
Route::get('/packages/{id}', [PackageController::class, 'show'])->name('packages.show');
Route::get('/packages/form/designing-tailor-made-trips-for-affiliated-agencies', [PackageController::class, 'form_1'])->name('packages.form_1');
Route::get('/packages/form/design-your-tailor-made-trip-to-sri-lanka-with-a-local-travel-agency', [PackageController::class, 'form_2'])->name('packages.form_2');

Route::post('/bookings', [BookingController::class, 'store'])->name('bookings.store');
Route::post('/bookings/form_1_store', [BookingController::class, 'form_1_store'])->name('bookings.form_1_store');
Route::post('/bookings/form_2_store', [BookingController::class, 'form_2_store'])->name('bookings.form_2_store');

Route::middleware(['auth'])
  ->prefix('admin')
  ->group(function () {


    Route::get('/dashboard', [Analytics::class, 'index'])->name('dashboard')
      ->middleware('role:Super Admin|Admin|Travel Consultant|Accountant|Foreign Agent|Foreign Agency|Chauffeur|Customer');

      Route::middleware('role:Super Admin|Admin|Accountant|Travel Consultant')->group(function () {
        Route::get('reports/quotation', [ReportController::class, 'quotation'])->name('reports.quotation');
        Route::get('reports/hotel-bookings', [ReportController::class, 'hotelBookings'])->name('reports.hotelBookings');
        Route::get('reports/transport-bookings', [ReportController::class, 'transportReport'])->name('reports.transportReport');
        Route::get('reports/activity-bookings', [ReportController::class, 'activityReport'])->name('reports.activityReport');


        Route::get('/quotations/documents', [QuotationDocumentsController::class, 'index'])->name('quotations.documents.index');
        Route::get('/quotations/documents/pull', [QuotationDocumentsController::class, 'show'])->name('quotations.documents');
        Route::post('/quotations/hotels/documents/{hotelId}', [QuotationDocumentsController::class, 'storeHotelDocument'])->name('quotations.hotels.documents');
        Route::post('/quotations/attractions/documents/{attractionId}', [QuotationDocumentsController::class, 'storeAttractionDocument'])->name('quotations.attractions.documents');
    });

    Route::middleware('role:Super Admin|Admin|Travel Consultant|Foreign Agent|Foreign Agency')->group(function () {
      Route::get('/bookings', [BookingController::class, 'index'])->name('bookings.index');
      Route::get('/bookings/list', [BookingController::class, 'getBookings'])->name('bookings.list');
      Route::patch('/bookings/{booking}/status', [BookingController::class, 'updateStatus'])->name('bookings.updateStatus');

      Route::prefix('bookings')->group(function () {
          Route::patch('{id}/status-01', [BookingController::class, 'updateStatus01']);
          Route::patch('{id}/status-02', [BookingController::class, 'updateStatus02']);
          Route::patch('{id}/status-03', [BookingController::class, 'updateStatus03']);
          Route::patch('{id}/status-03-custom', [BookingController::class, 'addStatus03Item']);
          Route::patch('{id}/update-reference-id', [BookingController::class, 'updateReferenceId']);
      });
    });

    Route::middleware('role:Super Admin|Admin|Travel Consultant')->group(function () {
      Route::patch('/bookings/{id}/assignee', [BookingController::class, 'updateAssignee'])->name('bookings.updateAssignee');
      Route::delete('/bookings/{booking}', [BookingController::class, 'destroy'])->name('bookings.destroy');
      // Booking routes
      Route::get('/create', [BookingController::class, 'create'])->name('bookings.create');
      // Route::post('/bookings', [BookingController::class, 'store'])->name('bookings.store')
    });

    Route::middleware('role:Super Admin|Admin|Travel Consultant|Foreign Agent|Foreign Agency')->group(function () {
      Route::patch('/running-jobs/{id}/quotation-assignees', [RunningJobController::class, 'updateQuotationAssignees']);
    });

    Route::prefix('running-jobs')->name('running_jobs.')->group(function () {
      Route::get('/', [RunningJobController::class, 'index'])->name('runningJobs.index'); // List all running jobs
      Route::get('/running-jobs/data', [RunningJobController::class, 'getRunningJobs'])->name('runningJobs.getRunningJobs');

      Route::get('/create', [RunningJobController::class, 'create'])->name('runningJobs.create');
      Route::post('/store', [RunningJobController::class, 'store'])->name('runningJobs.store');
      Route::post('/send-email/{id}', [RunningJobController::class, 'sendEmail'])->name('runningJobs.sendEmail');
      Route::get('/{id}/history', [RunningJobController::class, 'history'])->name('runningJobs.history');
      Route::patch('/{id}/update-quotation', [RunningJobController::class, 'updateQuotation'])->name('running_jobs.updateQuotation');

      Route::post('/{runningJobId}/conversation', [RunningJobController::class, 'addConversation'])
        ->name('runningJobs.addConversation');
    });


    Route::middleware('role:Super Admin|Admin|Travel Consultant|Accountant|Foreign Agent|Foreign Agency|Chauffeur|Customer')->group(function () {
      Route::get('profile/create', [ProfileController::class, 'create'])->name('profile.create');
      Route::get('profile/{id}', [ProfileController::class, 'show'])->name('profile.show');
      Route::post('profile', [ProfileController::class, 'store'])->name('profile.store');
      Route::get('profile/edit/{id}', [ProfileController::class, 'edit'])->name('profile.edit');
      Route::put('profile/{profile}', [ProfileController::class, 'update'])->name('profile.update');
      Route::delete('/profile/{id}', [ProfileController::class, 'destroy'])->name('company.destroy');
    });

    Route::middleware('role:Super Admin|Admin')->group(function () {
      Route::get('users', [ProfileController::class, 'index'])->name('profile.index');
      Route::get('users/get-users', [ProfileController::class, 'getUsers'])->name('profile.getUsers');
      Route::put('a_profile/{profile}', [ProfileController::class, 'a_update'])->name('profile.a_update');

      // WhatsApp Management Routes
      Route::prefix('whatsapp')->name('admin.whatsapp.')->group(function () {
        Route::get('/', [WhatsAppController::class, 'index'])->name('index');
        Route::post('/test', [WhatsAppController::class, 'test'])->name('test');
        Route::get('/users', [WhatsAppController::class, 'getUsersWithWhatsApp'])->name('users');
        Route::post('/bulk', [WhatsAppController::class, 'sendBulk'])->name('bulk');
      });
    });

    //Route::get('company', [CompanyController::class, 'index'])->name('company.index');
    //Route::get('company/create', [CompanyController::class, 'create'])->name('company.create');
    //Route::post('company', [CompanyController::class, 'store'])->name('company.store');
    //Route::get('company/edit/{id}', [CompanyController::class, 'edit'])->name('company.edit');
    //Route::put('company/{profile}', [CompanyController::class, 'update'])->name('company.update');
    //Route::get('company/get-companies', [CompanyController::class, 'getCompanies'])->name('company.getCompanies');
    //Route::delete('/company/{id}', [CompanyController::class, 'destroy'])->name('company.destroy');

    // User management
    Route::middleware('role:Super Admin|Admin')->group(function () {
      Route::get('roles', [RoleController::class, 'index'])->name('roles.index');
      Route::get('roles/create', [RoleController::class, 'create'])->name('roles.create');
      Route::post('roles', [RoleController::class, 'store'])->name('roles.store');
      Route::get('roles/edit/{id}', [RoleController::class, 'edit'])->name('roles.edit');
      Route::put('roles/{id}', [RoleController::class, 'update'])->name('roles.update');
      Route::get('roles/get-roles', [RoleController::class, 'getRoles'])->name('roles.getRoles');
      Route::delete('roles/{id}', [RoleController::class, 'destroy'])->name('roles.destroy');

      Route::get('permissions', [PermissionController::class, 'index'])->name('permissions.index');
      Route::get('permissions/create', [PermissionController::class, 'create'])->name('permissions.create');
      Route::post('permissions', [PermissionController::class, 'store'])->name('permissions.store');
      Route::get('permissions/edit/{id}', [PermissionController::class, 'edit'])->name('permissions.edit');
      Route::put('permissions/{id}', [PermissionController::class, 'update'])->name('permissions.update');
      Route::get('permissions/get-permissions', [PermissionController::class, 'getPermissions'])->name(
        'permissions.getPermissions'
      );
      Route::delete('permissions/{id}', [PermissionController::class, 'destroy'])->name('permissions.destroy');
    });

    // Customer management
    Route::middleware('role:Super Admin|Admin|Travel Consultant')->group(function () {
        Route::get('customers', [CustomerController::class, 'index'])->name('customers.index');
        Route::get('customers/get-customers', [CustomerController::class, 'getCustomers'])->name('customers.getCustomers');
        Route::get('customers/create', [CustomerController::class, 'create'])->name('customers.create');
        Route::post('customers', [CustomerController::class, 'store'])->name('customers.store');
        Route::get('customers/edit/{id}', [CustomerController::class, 'edit'])->name('customers.edit');
        Route::put('customers/{id}', [CustomerController::class, 'update'])->name('customers.update');
        Route::delete('customers/{id}', [CustomerController::class, 'destroy'])->name('customers.destroy');
    });

    Route::middleware('role:Super Admin|Admin|Travel Consultant|Foreign Agent|Foreign Agency')->group(function () {
      Route::get('places', [PlaceController::class, 'index'])->name('places.index');
      Route::get('places/get-places', [PlaceController::class, 'getPlaces'])->name(
        'places.getPlaces'
      );
    });
    Route::middleware('role:Super Admin|Admin|Travel Consultant')->group(function () {
      Route::get('places/create', [PlaceController::class, 'create'])->name('places.create');
      Route::post('places', [PlaceController::class, 'store'])->name('places.store');
      Route::get('places/edit/{id}', [PlaceController::class, 'edit'])->name('places.edit');
      Route::put('places/{id}', [PlaceController::class, 'update'])->name('places.update');
      Route::delete('places/{id}', [PlaceController::class, 'destroy'])->name('places.destroy');
      Route::delete('/places/{id}/delete-image', [PlaceController::class, 'deleteImage'])->name('places.deleteImage');
    });


    Route::middleware('role:Super Admin|Admin|Travel Consultant|Foreign Agent|Foreign Agency')->group(function () {
      Route::get('hotels', [HotelAdminController::class, 'index'])->name('hotels.index');
      Route::get('hotels/get-hotels', [HotelAdminController::class, 'getHotels'])->name('hotels.getHotels');
    });
    Route::middleware('role:Super Admin|Admin|Travel Consultant')->group(function () {
      Route::get('hotels/create', [HotelAdminController::class, 'create'])->name('hotels.create');
      Route::post('hotels', [HotelAdminController::class, 'store'])->name('hotels.store');
      Route::get('hotels/edit/{id}', [HotelAdminController::class, 'edit'])->name('hotels.edit');
      Route::put('hotels/{id}', [HotelAdminController::class, 'update'])->name('hotels.update');
      Route::delete('hotels/{id}', [HotelAdminController::class, 'destroy'])->name('hotels.destroy');
      Route::delete('/hotels/{id}/delete-image', [HotelAdminController::class, 'deleteImage'])->name('hotels.deleteImage');

      Route::get('hotels/filter/search', [HotelAdminController::class, 'search'])->name('hotels.search');
    });



    Route::middleware('role:Super Admin|Admin|Travel Consultant|Foreign Agent|Foreign Agency')->group(function () {
      Route::get('hotel-meals', [HotelMealAdminController::class, 'index'])->name('hotel-meals.index');
      Route::get('hotel-meals/get-meals', [HotelMealAdminController::class, 'getMeals'])->name('hotel-meals.getMeals');
    });
    Route::middleware('role:Super Admin|Admin|Travel Consultant')->group(function () {
      Route::get('hotel-meals/create', [HotelMealAdminController::class, 'create'])->name('hotel-meals.create');
      Route::post('hotel-meals', [HotelMealAdminController::class, 'store'])->name('hotel-meals.store');
      Route::get('hotel-meals/edit/{id}', [HotelMealAdminController::class, 'edit'])->name('hotel-meals.edit');
      Route::put('hotel-meals/{id}', [HotelMealAdminController::class, 'update'])->name('hotel-meals.update');
      Route::delete('hotel-meals/{id}', [HotelMealAdminController::class, 'destroy'])->name('hotel-meals.destroy');
    });

    Route::middleware('role:Super Admin|Admin|Travel Consultant|Foreign Agent|Foreign Agency')->group(function () {
      Route::get('hotel-categories', [HotelCategoryAdminController::class, 'index'])->name('hotel-categories.index');
      Route::get('hotel-categories/get-categories', [HotelCategoryAdminController::class, 'getCategories'])->name('hotel-categories.getCategories');
    });
    Route::middleware('role:Super Admin|Admin|Travel Consultant')->group(function () {
      Route::get('hotel-categories/create', [HotelCategoryAdminController::class, 'create'])->name('hotel-categories.create');
      Route::post('hotel-categories', [HotelCategoryAdminController::class, 'store'])->name('hotel-categories.store');
      Route::get('hotel-categories/edit/{id}', [HotelCategoryAdminController::class, 'edit'])->name('hotel-categories.edit');
      Route::put('hotel-categories/{id}', [HotelCategoryAdminController::class, 'update'])->name('hotel-categories.update');
      Route::delete('hotel-categories/{id}', [HotelCategoryAdminController::class, 'destroy'])->name('hotel-categories.destroy');
    });

    Route::middleware('role:Super Admin|Admin|Travel Consultant')->group(function () {
      Route::get('room-rates/hotel-room-rates', [HotelRoomRateAdminController::class, 'index'])->name('room-rates.index');
      Route::get('room-rates/get-rates', [HotelRoomRateAdminController::class, 'getRates'])->name('room-rates.getRates');
      Route::post('room-rates/update-rate', [HotelRoomRateAdminController::class, 'updateRate'])->name('room-rates.updateRate');
      Route::get('room-rates/import-hotel-rates', [HotelRoomRateAdminController::class, 'googleSheetsID'])->name('room-rates.import');
      Route::post('room-rates/hotel-room-rates/import', [HotelRoomRateAdminController::class, 'importFromGoogleSheet'])->name('hotel-room-rates.import');
      Route::post('room-rates/delete', [HotelRoomRateAdminController::class, 'delete'])->name('room-rates.delete');
      Route::post('room-rates/bulk-delete', [HotelRoomRateAdminController::class, 'bulkDelete'])->name('room-rates.bulkDelete');
      Route::get('hotel-room-rates/create', [HotelRoomRateAdminController::class, 'create'])->name('hotel-room-rates.create');
      Route::post('hotel-room-rates/store', [HotelRoomRateAdminController::class, 'store'])->name('hotel-room-rates.store');
      Route::get('hotel-room-rates/{id}/edit', [HotelRoomRateAdminController::class, 'edit'])->name('hotel-room-rates.edit');
      Route::put('hotel-room-rates/{id}', [HotelRoomRateAdminController::class, 'update'])->name('hotel-room-rates.update');
    });

    Route::middleware('role:Super Admin|Admin|Travel Consultant|Foreign Agent|Foreign Agency')->group(function () {
      Route::get('attractions', [AttractionAdminController::class, 'index'])->name('attractions.index');
      Route::get('attractions/get-attractions', [AttractionAdminController::class, 'getAttractions'])->name('attractions.getAttractions');
    });
    Route::middleware('role:Super Admin|Admin|Travel Consultant')->group(function () {
      Route::get('attractions/create', [AttractionAdminController::class, 'create'])->name('attractions.create');
      Route::post('attractions', [AttractionAdminController::class, 'store'])->name('attractions.store');
      Route::get('attractions/edit/{id}', [AttractionAdminController::class, 'edit'])->name('attractions.edit');
      Route::put('attractions/{id}', [AttractionAdminController::class, 'update'])->name('attractions.update');
      Route::delete('/attractions/{id}/delete-image', [AttractionAdminController::class, 'deleteImage'])->name('attractions.deleteImage');

      Route::delete('attractions/{id}', [AttractionAdminController::class, 'destroy'])->name('attractions.destroy');
    });

    Route::middleware('role:Super Admin|Admin|Travel Consultant|Foreign Agent|Foreign Agency')->group(function () {
      Route::get('citytours', [CityTourAdminController::class, 'index'])->name('citytours.index');
      Route::get('citytours/get-citytours', [CityTourAdminController::class, 'getCityTours'])->name('citytours.getCityTours');
    });
    Route::middleware('role:Super Admin|Admin|Travel Consultant')->group(function () {
      Route::get('citytours/create', [CityTourAdminController::class, 'create'])->name('citytours.create');
      Route::post('citytours', [CityTourAdminController::class, 'store'])->name('citytours.store');
      Route::get('citytours/edit/{id}', [CityTourAdminController::class, 'edit'])->name('citytours.edit');
      Route::put('citytours/{id}', [CityTourAdminController::class, 'update'])->name('citytours.update');

      Route::delete('citytours/{id}', [CityTourAdminController::class, 'destroy'])->name('citytours.destroy');
    });

    Route::middleware('role:Super Admin|Admin|Travel Consultant|Foreign Agent|Foreign Agency')->group(function () {
      Route::get('excursions', [ExcursionAdminController::class, 'index'])->name('excursions.index');
      Route::get('excursions/get-excursions', [ExcursionAdminController::class, 'getExcursions'])->name('excursions.getExcursions');
    });
    Route::middleware('role:Super Admin|Admin|Travel Consultant')->group(function () {
      Route::get('excursions/create', [ExcursionAdminController::class, 'create'])->name('excursions.create');
      Route::post('excursions', [ExcursionAdminController::class, 'store'])->name('excursions.store');
      Route::get('excursions/edit/{id}', [ExcursionAdminController::class, 'edit'])->name('excursions.edit');
      Route::put('excursions/{id}', [ExcursionAdminController::class, 'update'])->name('excursions.update');
      Route::delete('excursions/{id}', [ExcursionAdminController::class, 'destroy'])->name('excursions.destroy');
    });

    Route::middleware('role:Super Admin|Admin|Travel Consultant')->group(function () {
      Route::get('attraction-rates', [AttractionRateAdminController::class, 'index'])->name('attraction-rates.index');
      Route::get('attraction-rates/get-rates', [AttractionRateAdminController::class, 'getRates'])->name('attraction-rates.getRates');
      Route::post('attraction-rates/update-rate', [AttractionRateAdminController::class, 'updateRate'])->name('attraction-rates.updateRate');
      Route::post('attraction-rates/delete', [AttractionRateAdminController::class, 'delete'])->name('attraction-rates.delete');
      Route::post('attraction-rates/bulk-delete', [AttractionRateAdminController::class, 'bulkDelete'])->name('attraction-rates.bulkDelete');
      Route::get('attraction-rates/create', [AttractionRateAdminController::class, 'create'])->name('attraction-rates.create');
      Route::post('attraction-rates/store', [AttractionRateAdminController::class, 'store'])->name('attraction-rates.store');
      Route::get('attraction-rates/{id}/edit', [AttractionRateAdminController::class, 'edit'])->name('attraction-rates.edit');
      Route::put('attraction-rates/{id}', [AttractionRateAdminController::class, 'update'])->name('attraction-rates.update');
    });

    Route::middleware('role:Super Admin|Admin|Travel Consultant')->group(function () {
      Route::get('citytour-rates', [CityTourRateAdminController::class, 'index'])->name('citytour-rates.index');
      Route::get('citytour-rates/get-rates', [CityTourRateAdminController::class, 'getRates'])->name('citytour-rates.getRates');
      Route::post('citytour-rates/update-rate', [CityTourRateAdminController::class, 'updateRate'])->name('citytour-rates.updateRate');
      Route::post('citytour-rates/delete', [CityTourRateAdminController::class, 'delete'])->name('citytour-rates.delete');
      Route::post('citytour-rates/bulk-delete', [CityTourRateAdminController::class, 'bulkDelete'])->name('citytour-rates.bulkDelete');
      Route::get('citytour-rates/create', [CityTourRateAdminController::class, 'create'])->name('citytour-rates.create');
      Route::post('citytour-rates/store', [CityTourRateAdminController::class, 'store'])->name('citytour-rates.store');
      Route::get('citytour-rates/{id}/edit', [CityTourRateAdminController::class, 'edit'])->name('citytour-rates.edit');
      Route::put('citytour-rates/{id}', [CityTourRateAdminController::class, 'update'])->name('citytour-rates.update');
    });

    Route::middleware('role:Super Admin|Admin|Travel Consultant')->group(function () {
      Route::get('excursion-rates', [ExcursionRateAdminController::class, 'index'])->name('excursion-rates.index');
      Route::get('excursion-rates/get-rates', [ExcursionRateAdminController::class, 'getRates'])->name('excursion-rates.getRates');
      Route::post('excursion-rates/update-rate', [ExcursionRateAdminController::class, 'updateRate'])->name('excursion-rates.updateRate');
      Route::post('excursion-rates/delete', [ExcursionRateAdminController::class, 'delete'])->name('excursion-rates.delete');
      Route::post('excursion-rates/bulk-delete', [ExcursionRateAdminController::class, 'bulkDelete'])->name('excursion-rates.bulkDelete');
      Route::get('excursion-rates/create', [ExcursionRateAdminController::class, 'create'])->name('excursion-rates.create');
      Route::post('excursion-rates/store', [ExcursionRateAdminController::class, 'store'])->name('excursion-rates.store');
      Route::get('excursion-rates/{id}/edit', [ExcursionRateAdminController::class, 'edit'])->name('excursion-rates.edit');
      Route::put('excursion-rates/{id}', [ExcursionRateAdminController::class, 'update'])->name('excursion-rates.update');
    });

    Route::middleware('role:Super Admin|Admin|Travel Consultant|Foreign Agent')->group(function () {
      Route::get('vehicle-types', [VehicleTypeAdminController::class, 'index'])->name('vehicle-types.index');
      Route::get('vehicle-types/get-vehicle-types', [VehicleTypeAdminController::class, 'getVehicleTypes'])->name('vehicle-types.getVehicleTypes');
    });
    Route::middleware('role:Super Admin|Admin|Travel Consultant')->group(function () {
      Route::get('vehicle-types/create', [VehicleTypeAdminController::class, 'create'])->name('vehicle-types.create');
      Route::post('vehicle-types', [VehicleTypeAdminController::class, 'store'])->name('vehicle-types.store');
      Route::get('vehicle-types/edit/{id}', [VehicleTypeAdminController::class, 'edit'])->name('vehicle-types.edit');
      Route::put('vehicle-types/{id}', [VehicleTypeAdminController::class, 'update'])->name('vehicle-types.update');
      Route::delete('vehicle-types/{id}', [VehicleTypeAdminController::class, 'destroy'])->name('vehicle-types.destroy');
    });

    Route::middleware('role:Super Admin|Admin|Travel Consultant')->group(function () {
      Route::get('vehicle-rates', [VehicleRateAdminController::class, 'index'])->name('vehicle-rates.index');
      Route::get('vehicle-rates/get-rates', [VehicleRateAdminController::class, 'getRates'])->name('vehicle-rates.getRates');
      Route::post('vehicle-rates/update-rate', [VehicleRateAdminController::class, 'updateRate'])->name('vehicle-rates.updateRate');
      Route::post('vehicle-rates/delete', [VehicleRateAdminController::class, 'delete'])->name('vehicle-rates.delete');
      Route::post('vehicle-rates/bulk-delete', [VehicleRateAdminController::class, 'bulkDelete'])->name('vehicle-rates.bulkDelete');
      Route::get('vehicle-rates/create', [VehicleRateAdminController::class, 'create'])->name('vehicle-rates.create');
      Route::post('vehicle-rates/store', [VehicleRateAdminController::class, 'store'])->name('vehicle-rates.store');
      Route::get('vehicle-rates/{id}/edit', [VehicleRateAdminController::class, 'edit'])->name('vehicle-rates.edit');
      Route::put('vehicle-rates/{id}', [VehicleRateAdminController::class, 'update'])->name('vehicle-rates.update');
    });


    Route::middleware('role:Super Admin|Admin|Travel Consultant|Foreign Agent')->group(function () {
      Route::get('members', [MemberController::class, 'index'])->name('members.index');
      Route::get('members/get-members', [MemberController::class, 'getMembers'])->name('members.getMembers');
    });

    Route::middleware('role:Super Admin|Admin|Travel Consultant')->group(function () {
      Route::get('members/create', [MemberController::class, 'create'])->name('members.create');
      Route::get('members/{id}', [MemberController::class, 'show'])->name('members.show');
      Route::post('members', [MemberController::class, 'store'])->name('members.store');
      Route::get('members/edit/{id}', [MemberController::class, 'edit'])->name('members.edit');
      Route::put('members/{id}', [MemberController::class, 'update'])->name('members.update');
      Route::delete('members/{id}', [MemberController::class, 'destroy'])->name('members.destroy');
    });

    Route::middleware('role:Super Admin|Admin|Travel Consultant')->group(function () {
      Route::get('member-prices', [MemberPriceController::class, 'index'])->name('member-prices.index');
      Route::get('member-prices/create', [MemberPriceController::class, 'create'])->name('member-prices.create');
      Route::post('member-prices', [MemberPriceController::class, 'store'])->name('member-prices.store');
      Route::get('member-prices/edit/{id}', [MemberPriceController::class, 'edit'])->name('member-prices.edit');
      Route::put('member-prices/{id}', [MemberPriceController::class, 'update'])->name('member-prices.update');
      Route::delete('member-prices/{id}', [MemberPriceController::class, 'destroy'])->name('member-prices.destroy');
      Route::get('member-prices/get-member-prices', [MemberPriceController::class, 'getMemberPrices'])->name('member-prices.getMemberPrices');
    });

    Route::middleware('role:Super Admin|Admin|Travel Consultant|Foreign Agent|Foreign Agency')->group(function () {
      Route::get('admin-fee-prices', [AdminFeePriceController::class, 'index'])->name('admin-fee-prices.index');
      Route::get('admin-fee-prices/get-admin-fee-prices', [AdminFeePriceController::class, 'getAdminFeePrices'])->name('admin-fee-prices.getAdminFeePrices');
    });

    Route::middleware('role:Super Admin|Admin|Travel Consultant')->group(function () {
        Route::get('admin-fee-prices/create', [AdminFeePriceController::class, 'create'])->name('admin-fee-prices.create');
        Route::post('admin-fee-prices', [AdminFeePriceController::class, 'store'])->name('admin-fee-prices.store');
        Route::get('admin-fee-prices/edit/{id}', [AdminFeePriceController::class, 'edit'])->name('admin-fee-prices.edit');
        Route::put('admin-fee-prices/{id}', [AdminFeePriceController::class, 'update'])->name('admin-fee-prices.update');
        Route::delete('admin-fee-prices/{id}', [AdminFeePriceController::class, 'destroy'])->name('admin-fee-prices.destroy');
    });

    Route::get('member-prices/get-member-price', [MemberPriceController::class, 'getMemberPrice']);
    Route::post('member/create-box', [MemberPriceController::class, 'createMemberBox'])->name('member.createMemberBox');
    Route::get('member/create-add-box', [MemberPriceController::class, 'createAddMemberBox'])->name('hotel.createAddMemberBox');

    Route::get('admin_fee-prices/get-admin_fee-price', [AdminFeePriceController::class, 'getAdminFeePrice']);
    Route::post('admin_fee/create-box', [AdminFeePriceController::class, 'createAdminFeeBox'])->name('member.createAdminFeeBox');
    Route::get('admin_fee/create-add-box', [AdminFeePriceController::class, 'createAddAdminFeeBox'])->name('hotel.createAddAdminFeeBox');


    Route::middleware('role:Super Admin|Admin|Travel Consultant|Foreign Agent|Chauffeur')->group(function () {
      Route::prefix('operation')->group(function () {
        Route::get('member-allocates', [MemberAllocateController::class, 'index'])->name('member-allocates.index');
        Route::get('member-allocates/get-allocations', [MemberAllocateController::class, 'getAllocations'])->name('member-allocates.getAllocations');
      });
    });

    Route::middleware('role:Super Admin|Admin|Travel Consultant')->group(function () {
      Route::prefix('operation')->group(function () {
        Route::get('member-allocates/create', [MemberAllocateController::class, 'create'])->name('member-allocates.create');
        Route::post('member-allocates', [MemberAllocateController::class, 'store'])->name('member-allocates.store');
        Route::get('member-allocates/edit/{id}', [MemberAllocateController::class, 'edit'])->name('member-allocates.edit');
        Route::put('member-allocates/{id}', [MemberAllocateController::class, 'update'])->name('member-allocates.update');
        Route::delete('member-allocates/{id}', [MemberAllocateController::class, 'destroy'])->name('member-allocates.destroy');
      });
    });


    Route::get('quotation/start', [QuotationController::class, 'start'])->name('quotation.start');
    Route::post('quotation/start', [QuotationController::class, 'start_store'])->name('quotation.start_store');
    Route::get('quotation/content', [QuotationController::class, 'content'])->name('quotation.content');
    Route::post('quotation/content', [QuotationController::class, 'content_store'])->name('quotation.content_store');
    Route::post('quotation/session-save', [QuotationController::class, 'session_save'])->name('quotation.session_save');
    Route::get('quotation/day', [QuotationController::class, 'showDay'])->name('quotation.showDay');
    Route::post('quotation/save', [QuotationController::class, 'save'])->name('quotation.save');
    Route::get('quotation/finish', [QuotationController::class, 'finish'])->name('quotation.finish');
    Route::get('services/users', [ServiceController::class, 'getUsers'])->name('services.users');


    Route::post('quotation/summery/save-summery-ul', [QuotationController::class, 'saveSummeryUl'])->name('quotation.saveSummeryUl');
    Route::post('quotation/summery/save-supplements', [QuotationController::class, 'saveSupplements'])->name('quotation.saveSupplements');
    Route::post('quotation/comments/save-comment', [QuotationController::class, 'saveComment'])->name('quotation.saveComment');
    Route::post('quotation/summery/save-duration', [QuotationController::class, 'saveDuration'])->name('quotation.saveDuration');
    Route::get('quotations/preview/{token}', [QuotationController::class, 'previewByToken'])->name('quotations.preview');

    Route::post('quotation/review', [QuotationController::class, 'review'])->name('quotation.review');
    Route::post('quotation/delete', [QuotationController::class, 'delete'])->name('quotation.delete');
    Route::post('quotation/use-template', [QuotationController::class, 'useTemplate'])->name('quotation.useTemplate');

    Route::get('quotation/custom-quotation-review/{id}/{quotation_no}', [QuotationController::class, 'customQuotationReview'])->name('quotation.customQuotationReview');
    Route::get('quotation/custom-pnl-review/{id}/{quotation_no}', [QuotationController::class, 'customPNLReview'])->name('quotation.customPNLReview');
    Route::get('quotation/send/custom-send-hotel-vouchers/{id}/{quotation_no}', [QuotationController::class, 'customSendHotelVoucher'])->name('quotation.customSendHotelVoucher');
    Route::post('quotation/send/hotel-vouchers', [QuotationController::class, 'sendHotelVoucher'])->name('quotation.sendHotelVoucher');
    Route::post('quotation/hotel-vouchers/save-remarks', [QuotationController::class, 'saveRemarks'])->name('quotation.saveRemarks');
    Route::get('quotation/custom-confirmation-report/{id}/{quotation_no}', [QuotationController::class, 'confirmationReport'])->name('quotation.confirmationReport');
    Route::get('quotation/custom-confirmation-report-pdf/{id}/{quotation_no}', [QuotationController::class, 'confirmationReportPdf'])->name('quotation.confirmationReport');
    Route::post('quotation/hotel-confirmation/update', [QuotationController::class, 'updateStatus'])->name('hotel.confirmation.update');
    Route::post('quotation/hotel-vouchers/update-hotel-rate', [QuotationController::class, 'updateHotelRate']);
    Route::post('quotation/hotel-vouchers/update-discount-title', [QuotationController::class, 'updateDiscountTitle']);

    Route::get('quotations/list', [QuotationManageController::class, 'getList'])->name('quotations.getList');
    Route::get('quotation/index', [QuotationManageController::class, 'index'])->name('quotation.index');
    Route::get('quotations/search', [QuotationManageController::class, 'search'])->name('quotations.search');
    Route::get('quotations/{id}/details', [QuotationManageController::class, 'details'])->name('quotations.details');
    Route::post('quotations/toggle-status', [QuotationManageController::class, 'toggleStatus'])->name('quotations.toggleStatus');
    Route::get('quotations/get-version-status', [QuotationManageController::class, 'getVersionStatus'])->name('quotations.getVersionStatus');

    Route::get('quotation/get-vehicle-rate', [TransportController::class, 'getVehicleRate'])->name('quotation.getVehicleRate');
    Route::post('quotation/calculate-distance', [TransportController::class, 'calculateDistance']);

    Route::post('templates/store', [TemplateController::class, 'store'])->name('templates.store');
    Route::get('templates/index', [TemplateController::class, 'index'])->name('templates.index');
    Route::get('templates/edit/{id}', [TemplateController::class, 'edit'])->name('templates.edit');
    Route::put('templates/{id}', [TemplateController::class, 'update'])->name('templates.update');
    Route::delete('templates/{id}', [TemplateController::class, 'destroy'])->name('templates.destroy');

    Route::get('services/suggestions', [ServiceController::class, 'getModels'])->name('services.getModels');
    Route::get('hotels/search', [HotelController::class, 'getHotels'])->name('search.getHotels');
    Route::post('hotel/details/{id}', [HotelController::class, 'getHotelDetails'])->name('hotel.getHotelDetails');
    Route::post('hotel/rates/{id}', [HotelController::class, 'getHotelRates'])->name('hotel.getHotelRates');
    Route::post('hotel/create-box', [HotelController::class, 'createHotelBox'])->name('hotel.createHotelBox');
    Route::get('hotel/create-add-box', [HotelController::class, 'createAddHotelBox'])->name('hotel.createAddHotelBox');


    Route::get('attractions/search', [AttractionController::class, 'search'])->name('attraction.search');
    Route::post('attraction/details/{type}/{id}', [AttractionController::class, 'getAttractionDetails'])->name('attraction.getAttractionDetails');
    Route::post('attraction/create-box', [AttractionController::class, 'createAttractionBox'])->name('attraction.createAttractionBox');
    Route::get('attraction/create-add-box', [AttractionController::class, 'createAddAttractionBox'])->name('attraction.createAddAttractionBox');

    Route::post('/set-currency', [QuotationController::class, 'setCurrency'])->name('set.currency');
    Route::post('/set-language', [QuotationController::class, 'setLanguage'])->name('set.language');

    Route::post('other/create-box', [OtherController::class, 'createOtherBox'])->name('attraction.createOtherBox');
    Route::get('other/create-add-box', [OtherController::class, 'createAddOtherBox'])->name('attraction.createAddOtherBox');


    Route::get('dev/quotation/show/session', [QuotationController::class, 'show_session'])->name('quotation_dev.show_session');
    Route::get('dev/quotation/show', [QuotationController::class, 'showQuotationSession'])->name('quotation_dev.show');
    Route::post('dev/quotation/save', [QuotationController::class, 'saveQuotationSession'])->name('quotation_dev.save');
    Route::get('dev/quotation/clear', [QuotationController::class, 'clearQuotationSession'])->name('quotation_dev.clear');


    Route::get('settings', [SettingsController::class, 'index'])->name('settings.index');
    Route::post('settings', [SettingsController::class, 'update'])->name('settings.update');


    // Route for fetching images
    Route::get('/available-images', function (Request $request) {
      $folder = $request->query('folder', ''); // Get folder from query parameter
      $path = public_path('uploads/' . $folder);

      if (!File::exists($path)) {
        return response()->json(['error' => 'Directory not found'], 404);
      }

      $items = File::files($path); // Get all files in folder
      $directories = File::directories($path); // Get all subfolders

      $images = [];
      $folders = [];

      // Get images
      foreach ($items as $file) {
        $images[] = [
          'name' => $file->getFilename(),
          'url' => asset('uploads/' . $folder . '/' . $file->getFilename()),
          'type' => 'file'
        ];
      }

      // Get folders
      foreach ($directories as $dir) {
        $folders[] = [
          'name' => basename($dir),
          'path' => $folder . '/' . basename($dir),
          'type' => 'folder'
        ];
      }

      return response()->json([
        'folders' => $folders,
        'images' => $images,
        'current_folder' => $folder // Return the current folder path
      ]);
    });

    // Route for image upload
    Route::post('/upload-image', [ImageController::class, 'uploadImage'])->name('upload.image');

  });
